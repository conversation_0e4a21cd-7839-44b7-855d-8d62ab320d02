import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddImageHashAndDuplicateFields1747822000000 implements MigrationInterface {
  name = 'AddImageHashAndDuplicateFields1747822000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add imageHash column
    await queryRunner.query(`
      ALTER TABLE "attachments" 
      ADD COLUMN "imageHash" character varying
    `);

    // Add originalAttachmentId column for self-referencing foreign key
    await queryRunner.query(`
      ALTER TABLE "attachments" 
      ADD COLUMN "originalAttachmentId" uuid
    `);

    // Add foreign key constraint for originalAttachmentId
    await queryRunner.query(`
      ALTER TABLE "attachments" 
      ADD CONSTRAINT "FK_attachments_originalAttachment" 
      FOREIGN KEY ("originalAttachmentId") 
      REFERENCES "attachments"("id") 
      ON DELETE SET NULL
    `);

    // Create index on imageHash for faster duplicate detection
    await queryRunner.query(`
      CREATE INDEX "IDX_attachments_imageHash" 
      ON "attachments" ("imageHash")
    `);

    // Create index on originalAttachmentId for faster queries
    await queryRunner.query(`
      CREATE INDEX "IDX_attachments_originalAttachmentId" 
      ON "attachments" ("originalAttachmentId")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_attachments_originalAttachmentId"`);
    await queryRunner.query(`DROP INDEX "IDX_attachments_imageHash"`);

    // Drop foreign key constraint
    await queryRunner.query(`ALTER TABLE "attachments" DROP CONSTRAINT "FK_attachments_originalAttachment"`);

    // Drop columns
    await queryRunner.query(`ALTER TABLE "attachments" DROP COLUMN "originalAttachmentId"`);
    await queryRunner.query(`ALTER TABLE "attachments" DROP COLUMN "imageHash"`);
  }
}
