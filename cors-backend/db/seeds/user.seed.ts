import { DataSource } from 'typeorm';
import { Role } from '../../src/roles/entities/role.entity';
import { User } from '../../src/users/entities/user.entity';
import * as bcrypt from 'bcryptjs';
import { permissions } from 'src/permissions/permissions';

const allPermission = permissions;

const permissionCount = allPermission.reduce(
  (acc, curr) => acc + curr.actions.length,
  0,
);

export async function seedDevUser(dataSource: DataSource) {
  const developerRole = await createRole(dataSource, {
    name: 'Developer',
    isActive: true,
    rolePermissions: allPermission,
    permissionCount,
  });

  const superAdminRole = await createRole(dataSource, {
    name: 'Super Admin',
    isActive: true,
    rolePermissions: allPermission,
    permissionCount,
  });

  const ownerRole = await createRole(dataSource, {
    name: 'Owner',
    isActive: true,
    rolePermissions: allPermission,
    permissionCount,
  });

  await createUser(dataSource, {
    firstName: 'Dev',
    lastName: 'User',
    email: '<EMAIL>',
    roles: [developerRole],
  });

  await createUser(dataSource, {
    firstName: 'Dev',
    lastName: 'Example',
    email: '<EMAIL>',
    roles: [superAdminRole],
  });

  await createUser(dataSource, {
    firstName: 'Owner',
    lastName: 'User',
    email: '<EMAIL>',
    roles: [developerRole],
  });

  await createUser(dataSource, {
    firstName: 'Chris',
    lastName: 'Anderson',
    email: '<EMAIL>',
    roles: [ownerRole],
  });

  await createUser(dataSource, {
    firstName: 'Ahmed',
    lastName: 'Usman',
    email: '<EMAIL>',
    roles: [ownerRole],
  });
  console.log('Development user seeded successfully');
}

const createUser = async (
  dataSource: DataSource,
  user: Pick<User, 'firstName' | 'lastName' | 'email' | 'roles'>,
) => {
  const userRepository = dataSource.getRepository(User);

  let userRecord = await userRepository.findOne({
    where: { email: user.email },
  });

  const hashedPassword = await bcrypt.hash('test1234', 10);

  if (userRecord) {
    userRepository.merge(userRecord, {
      ...user,
      password: hashedPassword,
      isActive: true,
    });
    await userRepository.save(userRecord);
    console.log(`User ${user.email} updated successfully`);
    return userRecord;
  }

  // Create new user if doesn't exist
  const newUser = userRepository.create({
    ...user,
    password: hashedPassword,
    isActive: true,
  });

  await userRepository.save(newUser);
  console.log(`User ${user.email} created successfully`);
  return newUser;
};

const createRole = async (
  dataSource: DataSource,
  role: Pick<Role, 'name' | 'isActive' | 'rolePermissions' | 'permissionCount'>,
) => {
  const roleRepository = dataSource.getRepository(Role);

  let roleRecord = await roleRepository.findOne({
    where: { name: role.name },
  });

  if (roleRecord) {
    // Update existing role
    roleRepository.merge(roleRecord, {
      isActive: role.isActive,
      rolePermissions: role.rolePermissions,
      permissionCount: role.permissionCount,
    });
    await roleRepository.save(roleRecord);
    console.log(`Role ${role.name} updated successfully`);
    return roleRecord;
  }

  // Create new role if doesn't exist
  const newRole = roleRepository.create({
    name: role.name,
    isActive: role.isActive,
    rolePermissions: role.rolePermissions,
    permissionCount: role.permissionCount,
  });
  await roleRepository.save(newRole);
  console.log(`Role ${role.name} created successfully`);
  return newRole;
};
