# Image Deduplication Implementation

## Overview

This implementation provides SHA256-based image deduplication for the CutoutPro workflow. When multiple line items contain identical images, the system will:

1. Calculate SHA256 hashes for all images
2. Identify duplicates across line items
3. Process only unique images with CutoutPro
4. Share the resulting CutoutPro URL among all duplicate images

## Key Components

### 1. ImageDeduplicationService (`src/common/image-deduplication.service.ts`)

**Main Methods:**
- `deduplicateAcrossLineItems(lineItems: LineItem[])` - Deduplicates images across multiple line items
- `deduplicateWithinLineItem(lineItem: LineItem)` - Deduplicates images within a single line item
- `applyUrlToDuplicates(cutoutProUrl, originalAttachment, duplicates)` - Applies CutoutPro URL to all duplicates

**Features:**
- SHA256 hash calculation and caching
- Database storage of image hashes
- Reference tracking between original and duplicate attachments
- Error handling for failed hash calculations

### 2. Enhanced Attachment Entity

**New Fields:**
- `imageHash: string` - SHA256 hash of the image content
- `originalAttachment: Attachment` - Reference to original if this is a duplicate
- `duplicateReferences: Attachment[]` - List of duplicates referencing this original

### 3. Enhanced WorkflowService

**New Methods:**
- `processMultipleLineItems(lineItemsWithSkus)` - Processes multiple line items with cross-deduplication
- `processCutoutProItemsWithDeduplication()` - Specialized CutoutPro processing with deduplication

## Usage Flow

### Single Line Item Processing (existing)
```typescript
await workflowService.processLineItem(lineItem, productSku);
```

### Multiple Line Items Processing (new)
```typescript
const lineItemsWithSkus = [
  { lineItem: lineItem1, productSku: sku1 },
  { lineItem: lineItem2, productSku: sku2 }
];
await workflowService.processMultipleLineItems(lineItemsWithSkus);
```

## Database Schema Changes

### Migration: `1747822000000-AddImageHashAndDuplicateFields.ts`

Adds:
- `imageHash` column to attachments table
- `originalAttachmentId` foreign key for self-referencing
- Indexes for performance optimization

## Example Scenarios

### Scenario 1: Identical Images Across Line Items
```
Line Item 1: [image1.jpg, image2.jpg, image3.jpg]
Line Item 2: [image1.jpg, image2.jpg, image4.jpg]

Result:
- Process: image1.jpg (once), image2.jpg (once), image3.jpg, image4.jpg
- Share CutoutPro URLs for image1.jpg and image2.jpg across both line items
```

### Scenario 2: All Images Identical
```
Line Item 1: [image1.jpg, image1.jpg, image1.jpg]
Line Item 2: [image1.jpg, image1.jpg]

Result:
- Process: image1.jpg (once)
- Share CutoutPro URL across all 5 attachment instances
```

## Performance Benefits

1. **Reduced CutoutPro API Calls**: Only unique images are processed
2. **Faster Processing**: Parallel processing of different workflow groups
3. **Cost Savings**: Fewer API calls to external services
4. **Database Optimization**: Indexed hash lookups for duplicate detection

## Error Handling

- Failed hash calculations are logged and images are treated as unique
- CutoutPro processing errors are isolated per attachment
- Database transaction safety for reference updates

## Testing

Run the test suite:
```bash
npm test -- --testPathPattern=image-deduplication.service.spec.ts
```

## Configuration

No additional configuration required. The service automatically:
- Calculates and stores image hashes on first processing
- Uses cached hashes for subsequent operations
- Maintains referential integrity between duplicates

## Monitoring

The service logs:
- Number of duplicates found per hash
- Failed hash calculations
- CutoutPro URL applications to duplicates

Check application logs for deduplication statistics and any processing errors.
