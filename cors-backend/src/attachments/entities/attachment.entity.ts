import { <PERSON>ti<PERSON>, Column, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntity } from 'src/common/base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { LineItem } from '../../orders/entities/line-item.entity';
import { LineItemRequest } from '../../orders/entities/line-item-request.entity';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { User } from 'src/users/entities/user.entity';

@Entity('attachments')
export class Attachment extends BaseEntity {
  @ApiProperty({ description: 'The name of the uploaded file' })
  @Column()
  filename: string;

  @ApiProperty({ description: 'The URL where the file can be accessed' })
  @Column()
  url: string;

  @ApiProperty({ description: 'The MIME type of the file' })
  @Column()
  mimetype: string;

  @ApiProperty({ description: 'The size of the file in bytes' })
  @Column()
  size: number;

  @ApiProperty({
    description: 'The current status of the attachment',
    example: 'pending',
  })
  @Column({ type: 'character varying', default: 'pending' })
  status: string;

  @ApiProperty({ description: 'Cutout Pro Image Reference URL' })
  @Column({ type: 'character varying', nullable: true })
  cutoutProImageUrl: string;

  @ManyToOne(() => LineItem, lineItem => lineItem.attachments, {
    nullable: true,
  })
  lineItem: LineItem;

  @ManyToOne(() => LineItemRequest, request => request.attachments, {
    nullable: true,
  })
  request: LineItemRequest;

  @ManyToOne(() => Queue, queue => queue.attachments, {
    nullable: true,
  })
  queue: Queue;

  @ManyToOne(() => User, user => user.attachments, {
    nullable: true,
  })
  assignedTo: User;

  @ApiProperty({ description: 'Completed Art File URL' })
  @Column({ type: 'character varying', nullable: true })
  completedArtFileUrl: string;

  @ApiProperty({ description: 'SHA256 hash of the image content for duplicate detection' })
  @Column({ type: 'character varying', nullable: true })
  imageHash: string;

  @ApiProperty({ description: 'Reference to the original attachment if this is a duplicate' })
  @ManyToOne(() => Attachment, attachment => attachment.duplicateReferences, {
    nullable: true,
  })
  originalAttachment: Attachment;

  @ApiProperty({ description: 'List of duplicate attachments that reference this original' })
  @OneToMany(() => Attachment, attachment => attachment.originalAttachment)
  duplicateReferences: Attachment[];
}
