import { Injectable, Logger, Inject } from '@nestjs/common';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  createWriteStream,
  existsSync,
  unlinkSync,
  mkdirSync,
  createReadStream,
} from 'fs';
import { join } from 'path';
import axios from 'axios';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import * as qs from 'qs';
import { accessEnv } from 'src/env.validation';
import { StorageService } from 'src/attachments/services/storage.service';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { Order } from 'src/orders/entities/order.entity';
import { checkStatusTransition } from 'src/utils/workflow.utils';
@Injectable()
export class CutoutProService {
  private readonly logger = new Logger(CutoutProService.name);
  private readonly API_KEY = accessEnv('CUTOUT_PRO_API_KEY');
  private readonly CUTOUT_PRO_URL = accessEnv('CUTOUT_PRO_API_URL');
  private readonly MAX_RETRIES = 3;
  private readonly RATE_LIMIT = 5;

  constructor(
    @InjectRepository(LineItem)
    private readonly lineItemRepository: Repository<LineItem>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    private storageService: StorageService,
    @InjectRepository(Attachment)
    private attachmentRepository: Repository<Attachment>,
    @InjectRepository(Queue)
    private queueRepository: Repository<Queue>,
  ) {}

  async process(
    lineItem: LineItem,
    attachments: Attachment[],
    cropType: string,
  ) {
    this.logger.log(`Cutout Pro Service called`);

    let allSuccess = true;
    for (const attachment of attachments) {
      const imageUrl = attachment.url;
      const imageId = attachment.id;
      await this.enforceRateLimit(lineItem.itemNumber);

      const params: any = {
        url: imageUrl,
        mattingType: cropType,
        crop: true,
        preview: false,
        outputFormat: 'png',
      };
      if (cropType === '6') params.cropMargin = 0;

      const uri = `${this.CUTOUT_PRO_URL}?${qs.stringify(params)}`;
      let retries = 0;
      let success = false;

      while (retries < this.MAX_RETRIES) {
        const responseBody = await this.makeRequest(uri);
        let imageUrl = '';
        if (responseBody && responseBody.code === 0) {
          const responseImageUrl = responseBody.data.imageUrl;
          imageUrl = await this.downloadAndAttachImage(responseImageUrl);
          if (imageUrl) {
            let cropReviewQueue = await this.queueRepository.findOne({
              where: { name: 'Crop Review' },
            });
            if (!cropReviewQueue) {
              cropReviewQueue = this.queueRepository.create({
                name: 'Crop Review',
              });
              cropReviewQueue =
                await this.queueRepository.save(cropReviewQueue);
            }
            await this.attachmentRepository.update(imageId, {
              cutoutProImageUrl: imageUrl,
              status: 'Ready for Review',
              queue: cropReviewQueue,
            });
            success = true;
            break;
          }
        }
        // Only update the attachment status if all retries failed
        if (retries === this.MAX_RETRIES - 1 && !success) {
          await this.attachmentRepository.update(imageId, {
            status: 'CutOut Pro Failed',
          });
        }
        retries++;
      }
      if (!success) {
        allSuccess = false;
      }
    }

    if (allSuccess) {
      await checkStatusTransition(lineItem.status, 'Crop Review');
      lineItem.status = 'Crop Review';
      await this.lineItemRepository.save(lineItem);
    } else {
      lineItem.flagged = true;
      lineItem.flagReason = 'CutOut Pro Failed';
      const order = await this.orderRepository.findOne({
        where: {
          id: lineItem.order.id,
        },
      });
      if (order) {
        order.flagged = true;
        order.flaggedAt = new Date();
        await this.orderRepository.save(order);
      }
      await this.lineItemRepository.save(lineItem);
    }
  }

  private async makeRequest(uri: string): Promise<any> {
    try {
      const response = await axios.get(uri, {
        headers: {
          Accept: 'application/json',
          APIKEY: this.API_KEY,
        },
      });
      return response.data;
    } catch (e) {
      this.logger.error(`makeRequest error: ${e.message}`);
      return null;
    }
  }

  private async downloadAndAttachImage(url: string) {
    const ext = url.split('.').pop()?.split('?')[0] || 'png';
    const imageName = `cutout_pro_image.${ext}`;
    const tmpPath = join(process.cwd(), 'tmp');
    const filePath = join(tmpPath, imageName);
    // Ensure tmp directory exists
    if (!existsSync(tmpPath)) {
      mkdirSync(tmpPath, { recursive: true });
    }
    const writer = createWriteStream(filePath);
    const response = await axios.get(url, { responseType: 'stream' });
    response.data.pipe(writer);
    await new Promise<void>((resolve, reject) =>
      writer.on('finish', () => resolve()).on('error', reject),
    );
    try {
      // Use the upload-image method from attachment controller
      const formData = new FormData();
      const fileBuffer = await this.readFileAsBuffer(filePath);
      const blob = new Blob([fileBuffer], { type: 'image/png' });
      formData.append('file', blob, imageName);

      // Convert the buffer to a proper file object that the storage service can handle
      const fileObject = {
        fieldname: 'file',
        originalname: imageName,
        encoding: '7bit',
        mimetype: `image/${ext}`,
        buffer: fileBuffer,
        size: fileBuffer.length,
      };
      const uploadResponse = await this.storageService.uploadFile(fileObject);
      // Return the uploaded image URL
      const imageUrl = uploadResponse.url;
      return imageUrl;
    } catch (error) {
      this.logger.error(`Failed to upload image: ${error.message}`);
      return '';
    } finally {
      // Clean up the downloaded file
      if (existsSync(filePath)) {
        unlinkSync(filePath);
      }
    }
  }

  private async enforceRateLimit(itemNumber: string) {
    const key = `cutout_pro:${itemNumber}`;
    const current = (await this.cacheManager.get<number>(key)) || 0;

    if (current >= this.RATE_LIMIT) {
      this.logger.warn('Rate limit reached, waiting...');
      await this.sleep(1000);
    }

    await this.cacheManager.set(key, current + 1, 1000); // TTL 1 second
    this.logger.debug('Request allowed');
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async readFileAsBuffer(filePath: string): Promise<Buffer> {
    const fileBuffer = await createReadStream(filePath);
    const chunks: Uint8Array[] = [];
    for await (const chunk of fileBuffer) {
      chunks.push(chunk);
    }
    return Buffer.concat(chunks);
  }
}
