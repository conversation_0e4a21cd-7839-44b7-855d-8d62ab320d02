import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ImageDeduplicationService } from './image-deduplication.service';
import { Attachment } from '../attachments/entities/attachment.entity';
import { LineItem } from '../orders/entities/line-item.entity';

describe('ImageDeduplicationService', () => {
  let service: ImageDeduplicationService;
  let attachmentRepo: Repository<Attachment>;

  const mockAttachmentRepo = {
    save: jest.fn(),
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImageDeduplicationService,
        {
          provide: getRepositoryToken(Attachment),
          useValue: mockAttachmentRepo,
        },
      ],
    }).compile();

    service = module.get<ImageDeduplicationService>(ImageDeduplicationService);
    attachmentRepo = module.get<Repository<Attachment>>(getRepositoryToken(Attachment));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('deduplicateWithinLineItem', () => {
    it('should return empty result for line item with no attachments', async () => {
      const lineItem = { attachments: [] } as LineItem;

      const result = await service.deduplicateWithinLineItem(lineItem);

      expect(result.uniqueAttachments).toHaveLength(0);
      expect(result.duplicateMap.size).toBe(0);
      expect(result.originalMap.size).toBe(0);
    });

    it('should handle line item with unique attachments', async () => {
      const attachment1 = {
        id: '1',
        url: 'https://example.com/image1.jpg',
        imageHash: null,
      } as Attachment;

      const attachment2 = {
        id: '2',
        url: 'https://example.com/image2.jpg',
        imageHash: null,
      } as Attachment;

      const lineItem = { attachments: [attachment1, attachment2] } as LineItem;

      // Mock the hash calculation to return different hashes
      jest.spyOn(service as any, 'calculateImageHash')
        .mockResolvedValueOnce('hash1')
        .mockResolvedValueOnce('hash2');

      mockAttachmentRepo.save.mockResolvedValue({});

      const result = await service.deduplicateWithinLineItem(lineItem);

      expect(result.uniqueAttachments).toHaveLength(2);
      expect(result.duplicateMap.size).toBe(0);
      expect(result.originalMap.size).toBe(2);
    });

    it('should detect duplicate attachments within line item', async () => {
      const attachment1 = {
        id: '1',
        url: 'https://example.com/image1.jpg',
        imageHash: null,
      } as Attachment;

      const attachment2 = {
        id: '2',
        url: 'https://example.com/image1.jpg', // Same URL as attachment1
        imageHash: null,
      } as Attachment;

      const lineItem = { attachments: [attachment1, attachment2] } as LineItem;

      // Mock the hash calculation to return the same hash for both
      jest.spyOn(service as any, 'calculateImageHash')
        .mockResolvedValue('same-hash');

      mockAttachmentRepo.save.mockResolvedValue({});

      const result = await service.deduplicateWithinLineItem(lineItem);

      expect(result.uniqueAttachments).toHaveLength(1);
      expect(result.duplicateMap.size).toBe(1);
      expect(result.duplicateMap.get('same-hash')).toHaveLength(1);
      expect(result.originalMap.get('same-hash')).toBe(attachment1);
    });
  });

  describe('deduplicateAcrossLineItems', () => {
    it('should detect duplicates across multiple line items', async () => {
      const attachment1 = {
        id: '1',
        url: 'https://example.com/image1.jpg',
        imageHash: null,
      } as Attachment;

      const attachment2 = {
        id: '2',
        url: 'https://example.com/image1.jpg', // Same URL as attachment1
        imageHash: null,
      } as Attachment;

      const lineItem1 = { attachments: [attachment1] } as LineItem;
      const lineItem2 = { attachments: [attachment2] } as LineItem;

      // Mock the hash calculation to return the same hash for both
      jest.spyOn(service as any, 'calculateImageHash')
        .mockResolvedValue('same-hash');

      mockAttachmentRepo.save.mockResolvedValue({});

      const result = await service.deduplicateAcrossLineItems([lineItem1, lineItem2]);

      expect(result.uniqueAttachments).toHaveLength(1);
      expect(result.duplicateMap.size).toBe(1);
      expect(result.duplicateMap.get('same-hash')).toHaveLength(1);
      expect(result.originalMap.get('same-hash')).toBe(attachment1);
    });
  });

  describe('applyUrlToDuplicates', () => {
    it('should apply CutoutPro URL to all duplicate attachments', async () => {
      const originalAttachment = {
        id: '1',
        cutoutProImageUrl: null,
      } as Attachment;

      const duplicate1 = {
        id: '2',
        cutoutProImageUrl: null,
        status: 'pending',
      } as Attachment;

      const duplicate2 = {
        id: '3',
        cutoutProImageUrl: null,
        status: 'pending',
      } as Attachment;

      const cutoutProUrl = 'https://cutoutpro.com/processed-image.jpg';

      mockAttachmentRepo.save.mockResolvedValue({});

      await service.applyUrlToDuplicates(cutoutProUrl, originalAttachment, [duplicate1, duplicate2]);

      expect(originalAttachment.cutoutProImageUrl).toBe(cutoutProUrl);
      expect(duplicate1.cutoutProImageUrl).toBe(cutoutProUrl);
      expect(duplicate1.status).toBe('Cutout Pro Completed');
      expect(duplicate2.cutoutProImageUrl).toBe(cutoutProUrl);
      expect(duplicate2.status).toBe('Cutout Pro Completed');

      expect(mockAttachmentRepo.save).toHaveBeenCalledWith(originalAttachment);
      expect(mockAttachmentRepo.save).toHaveBeenCalledWith([duplicate1, duplicate2]);
    });
  });

  describe('getDeduplicationStats', () => {
    it('should return correct statistics', () => {
      const uniqueAttachments = [
        { id: '1' } as Attachment,
        { id: '2' } as Attachment,
      ];

      const duplicateMap = new Map([
        ['hash1', [{ id: '3' } as Attachment, { id: '4' } as Attachment]],
        ['hash2', [{ id: '5' } as Attachment]],
      ]);

      const originalMap = new Map();

      const result = { uniqueAttachments, duplicateMap, originalMap };

      const stats = service.getDeduplicationStats(result);

      expect(stats.totalOriginal).toBe(5); // 2 unique + 3 duplicates
      expect(stats.totalUnique).toBe(2);
      expect(stats.totalDuplicates).toBe(3);
      expect(stats.duplicateGroups).toBe(2);
    });
  });
});
