import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { LineItem } from 'src/orders/entities/line-item.entity';
import * as crypto from 'crypto';
import * as https from 'https';
import * as http from 'http';

export interface ImageDeduplicationResult {
  uniqueAttachments: Attachment[];
  duplicateMap: Map<string, Attachment[]>; // hash -> array of duplicate attachments
  originalMap: Map<string, Attachment>; // hash -> original attachment to process
}

@Injectable()
export class ImageDeduplicationService {
  private readonly logger = new Logger(ImageDeduplicationService.name);

  constructor(
    @InjectRepository(Attachment)
    private attachmentRepo: Repository<Attachment>,
  ) {}

  /**
   * Process multiple line items and deduplicate images across all of them
   */
  async deduplicateAcrossLineItems(lineItems: LineItem[]): Promise<ImageDeduplicationResult> {
    const allAttachments: Attachment[] = [];
    
    // Collect all attachments from all line items
    for (const lineItem of lineItems) {
      if (lineItem.attachments && lineItem.attachments.length > 0) {
        allAttachments.push(...lineItem.attachments);
      }
    }

    return this.deduplicateAttachments(allAttachments);
  }

  /**
   * Process a single line item and deduplicate images within it
   */
  async deduplicateWithinLineItem(lineItem: LineItem): Promise<ImageDeduplicationResult> {
    if (!lineItem.attachments || lineItem.attachments.length === 0) {
      return {
        uniqueAttachments: [],
        duplicateMap: new Map(),
        originalMap: new Map(),
      };
    }

    return this.deduplicateAttachments(lineItem.attachments);
  }

  /**
   * Core deduplication logic that works with any array of attachments
   */
  private async deduplicateAttachments(attachments: Attachment[]): Promise<ImageDeduplicationResult> {
    const hashMap = new Map<string, Attachment[]>();
    const originalMap = new Map<string, Attachment>();
    const duplicateMap = new Map<string, Attachment[]>();

    // Calculate SHA256 hash for each attachment
    for (const attachment of attachments) {
      try {
        let hash = attachment.imageHash;

        // Calculate hash if not already stored
        if (!hash) {
          hash = await this.calculateImageHash(attachment.url);
          attachment.imageHash = hash;
          await this.attachmentRepo.save(attachment);
        }

        if (!hashMap.has(hash)) {
          hashMap.set(hash, []);
          originalMap.set(hash, attachment); // First occurrence is the original
        }

        hashMap.get(hash)!.push(attachment);
      } catch (error) {
        this.logger.error(`Failed to calculate hash for attachment ${attachment.id}: ${error.message}`);
        // If we can't calculate hash, treat as unique
        const uniqueHash = `unique_${attachment.id}`;
        hashMap.set(uniqueHash, [attachment]);
        originalMap.set(uniqueHash, attachment);
      }
    }

    // Separate unique and duplicate attachments
    const uniqueAttachments: Attachment[] = [];

    for (const [hash, attachmentGroup] of hashMap.entries()) {
      if (attachmentGroup.length === 1) {
        // Unique image
        uniqueAttachments.push(attachmentGroup[0]);
      } else {
        // Duplicate images - first one is original, rest are duplicates
        const [original, ...duplicates] = attachmentGroup;
        uniqueAttachments.push(original);
        duplicateMap.set(hash, duplicates);

        // Set up references between original and duplicates
        for (const duplicate of duplicates) {
          duplicate.originalAttachment = original;
        }

        // Save the duplicate references
        if (duplicates.length > 0) {
          await this.attachmentRepo.save(duplicates);
        }

        this.logger.log(`Found ${duplicates.length} duplicates for image hash ${hash.substring(0, 8)}...`);
      }
    }

    return {
      uniqueAttachments,
      duplicateMap,
      originalMap,
    };
  }

  /**
   * Apply CutoutPro URL to all duplicate attachments
   */
  async applyUrlToDuplicates(
    cutoutProUrl: string,
    originalAttachment: Attachment,
    duplicates: Attachment[]
  ): Promise<void> {
    try {
      // Update the original attachment
      originalAttachment.cutoutProImageUrl = cutoutProUrl;
      await this.attachmentRepo.save(originalAttachment);

      // Update all duplicate attachments with the same URL
      for (const duplicate of duplicates) {
        duplicate.cutoutProImageUrl = cutoutProUrl;
        duplicate.status = 'Cutout Pro Completed'; // Update status to indicate processing is done
      }

      if (duplicates.length > 0) {
        await this.attachmentRepo.save(duplicates);
        this.logger.log(`Applied CutoutPro URL to ${duplicates.length} duplicate attachments`);
      }
    } catch (error) {
      this.logger.error(`Failed to apply URL to duplicates: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calculate SHA256 hash of an image from its URL
   */
  private async calculateImageHash(imageUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('sha256');
      const client = imageUrl.startsWith('https:') ? https : http;

      const request = client.get(imageUrl, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }

        response.on('data', (chunk) => {
          hash.update(chunk);
        });

        response.on('end', () => {
          const hashValue = hash.digest('hex');
          resolve(hashValue);
        });

        response.on('error', (error) => {
          reject(error);
        });
      });

      request.on('error', (error) => {
        reject(error);
      });

      request.setTimeout(10000, () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  /**
   * Get statistics about deduplication results
   */
  getDeduplicationStats(result: ImageDeduplicationResult): {
    totalOriginal: number;
    totalUnique: number;
    totalDuplicates: number;
    duplicateGroups: number;
  } {
    const totalDuplicates = Array.from(result.duplicateMap.values())
      .reduce((sum, duplicates) => sum + duplicates.length, 0);

    return {
      totalOriginal: result.uniqueAttachments.length + totalDuplicates,
      totalUnique: result.uniqueAttachments.length,
      totalDuplicates,
      duplicateGroups: result.duplicateMap.size,
    };
  }
}
