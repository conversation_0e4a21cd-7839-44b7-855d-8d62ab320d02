import { Injectable } from '@nestjs/common';
import { Repository, In } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { CutoutProService } from './cutout-pro.service';
import * as crypto from 'crypto';
import * as https from 'https';
import * as http from 'http';

import {
  CroppingMethod,
  WorkflowCategory,
  CropType,
} from 'src/product-sku/enums/product-sku.enums';
import { checkStatusTransition } from 'src/utils/workflow.utils';
import { DBHelper } from 'src/helpers/db.helpers';

interface WorkflowRule {
  name: string;
  condition: (sku: ProductSku) => boolean;
  action: (
    lineItem: LineItem,
    sku: ProductSku,
    services: WorkflowServices,
  ) => Promise<void>;
}

interface WorkflowServices {
  lineItemRepo: Repository<LineItem>;
  queueRepo: Repository<Queue>;
  attachmentRepo: Repository<Attachment>;
  cutoutProApiService: CutoutProService;
}

@Injectable()
export class WorkflowService {
  constructor(
    @InjectRepository(LineItem)
    private lineItemRepo: Repository<LineItem>,
    @InjectRepository(Queue)
    private queueRepo: Repository<Queue>,
    @InjectRepository(Attachment)
    private attachmentRepo: Repository<Attachment>,
    private cutoutProApiService: CutoutProService,
  ) {}

  private workflows: WorkflowRule[] = [
    // Workflow 1: Crop Image Only or Crop Image and Template Placement
    {
      name: 'Crop Image Only or Crop Image and Template Placement',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_ONLY ||
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_AND_TEMPLATE,
      action: async (
        lineItem,
        sku,
        { lineItemRepo, queueRepo, attachmentRepo, cutoutProApiService },
      ) => {
        if (sku.croppingMethod === CroppingMethod.CUTOUT_PRO) {
          await checkStatusTransition(lineItem.status, 'Cutout Pro Requested');
          lineItem.status = 'Cutout Pro Requested';
          await lineItemRepo.save(lineItem);
          const attachments = lineItem.attachments;
          const cropType = sku.cropType === CropType.FACE_CUTOUT ? '3' : '6';
          
          // Simple deduplication within this line item
          const uniqueAttachments = await this.deduplicateAttachments(attachments);
          await cutoutProApiService.process(lineItem, uniqueAttachments, cropType);
        } else if (sku.croppingMethod === CroppingMethod.MANUAL) {
          let cropNeededQueue = await DBHelper.findOne(queueRepo, {
            where: { name: 'Crop Needed' },
          });
          if (!cropNeededQueue) {
            cropNeededQueue = queueRepo.create({
              name: 'Crop Needed',
            });
            cropNeededQueue = await queueRepo.save(cropNeededQueue);
          }
          const attachments = lineItem.attachments;
          for (const attachment of attachments) {
            attachment.status = 'Manual Crop Needed';
            attachment.queue = cropNeededQueue;
          }
          await checkStatusTransition(lineItem.status, 'Crop Needed');
          await attachmentRepo.save(attachments);
          lineItem.status = 'Crop Needed';
          await lineItemRepo.save(lineItem);
        }
      },
    },
    // Other workflows...
  ];

  /**
   * Simple SHA256 deduplication for attachments within a line item
   */
  private async deduplicateAttachments(attachments: Attachment[]): Promise<Attachment[]> {
    if (!attachments || attachments.length === 0) {
      return [];
    }

    const hashMap = new Map<string, Attachment>();
    const uniqueAttachments: Attachment[] = [];

    for (const attachment of attachments) {
      try {
        const hash = await this.calculateImageHash(attachment.url);
        
        if (!hashMap.has(hash)) {
          // First occurrence of this hash - keep it
          hashMap.set(hash, attachment);
          uniqueAttachments.push(attachment);
        } else {
          // Duplicate found - we'll share the CutoutPro URL later
          console.log(`Duplicate image found for attachment ${attachment.id}, will share URL from original`);
        }
      } catch (error) {
        console.error(`Failed to calculate hash for attachment ${attachment.id}:`, error);
        // If hash calculation fails, treat as unique
        uniqueAttachments.push(attachment);
      }
    }

    return uniqueAttachments;
  }

  /**
   * Calculate SHA256 hash of an image from its URL
   */
  private async calculateImageHash(imageUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('sha256');
      const client = imageUrl.startsWith('https:') ? https : http;

      const request = client.get(imageUrl, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }

        response.on('data', (chunk) => {
          hash.update(chunk);
        });

        response.on('end', () => {
          const hashValue = hash.digest('hex');
          resolve(hashValue);
        });

        response.on('error', (error) => {
          reject(error);
        });
      });

      request.on('error', (error) => {
        reject(error);
      });

      request.setTimeout(10000, () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  async processLineItem(lineItem: LineItem, productSku: ProductSku) {
    const services: WorkflowServices = {
      lineItemRepo: this.lineItemRepo,
      queueRepo: this.queueRepo,
      attachmentRepo: this.attachmentRepo,
      cutoutProApiService: this.cutoutProApiService,
    };

    // Set initial status
    if (!lineItem.status) {
      lineItem.status = 'Line Item Received';
      await this.lineItemRepo.save(lineItem);
    }

    // Apply workflows
    for (const workflow of this.workflows) {
      if (workflow.condition(productSku)) {
        await workflow.action(lineItem, productSku, services);
      }
    }
  }
}
