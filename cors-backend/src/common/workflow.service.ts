import { Injectable } from '@nestjs/common';
import { Repository, In, Not, IsNull } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { CutoutProService } from './cutout-pro.service';
import * as crypto from 'crypto';
import * as https from 'https';
import * as http from 'http';

import {
  CroppingMethod,
  WorkflowCategory,
  CropType,
} from 'src/product-sku/enums/product-sku.enums';
import { checkStatusTransition } from 'src/utils/workflow.utils';
import { DBHelper } from 'src/helpers/db.helpers';
interface WorkflowRule {
  name: string;
  condition: (sku: ProductSku) => boolean;
  action: (
    lineItem: LineItem,
    sku: ProductSku,
    services: WorkflowServices,
  ) => Promise<void>;
}

interface WorkflowServices {
  lineItemRepo: Repository<LineItem>;
  queueRepo: Repository<Queue>;
  attachmentRepo: Repository<Attachment>;
  cutoutProApiService: CutoutProService;
}

@Injectable()
export class WorkflowService {
  constructor(
    @InjectRepository(LineItem)
    private lineItemRepo: Repository<LineItem>,
    @InjectRepository(Queue)
    private queueRepo: Repository<Queue>,
    @InjectRepository(Attachment)
    private attachmentRepo: Repository<Attachment>,
    private cutoutProApiService: CutoutProService,
  ) {}

  private workflows: WorkflowRule[] = [
    // Workflow 1: Crop Image Only or Crop Image and Template Placement
    {
      name: 'Crop Image Only or Crop Image and Template Placement',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_ONLY ||
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_AND_TEMPLATE,
      action: async (
        lineItem,
        sku,
        { lineItemRepo, queueRepo, attachmentRepo, cutoutProApiService },
      ) => {
        if (sku.croppingMethod === CroppingMethod.CUTOUT_PRO) {
          await checkStatusTransition(lineItem.status, 'Cutout Pro Requested');
          lineItem.status = 'Cutout Pro Requested';
          await lineItemRepo.save(lineItem);
          const attachments = lineItem.attachments;
          const cropType = sku.cropType === CropType.FACE_CUTOUT ? '3' : '6';
          await cutoutProApiService.process(lineItem, attachments, cropType);
        } else if (sku.croppingMethod === CroppingMethod.MANUAL) {
          let cropNeededQueue = await DBHelper.findOne(queueRepo, {
            where: { name: 'Crop Needed' },
          });
          if (!cropNeededQueue) {
            cropNeededQueue = queueRepo.create({
              name: 'Crop Needed',
            });
            cropNeededQueue = await queueRepo.save(cropNeededQueue);
          }
          const attachments = lineItem.attachments;
          for (const attachment of attachments) {
            attachment.status = 'Manual Crop Needed';
            attachment.queue = cropNeededQueue;
          }
          await checkStatusTransition(lineItem.status, 'Crop Needed');
          await attachmentRepo.save(attachments);
          lineItem.status = 'Crop Needed';
          await lineItemRepo.save(lineItem);
        }
      },
    },
    // Workflow 2: Art Only
    {
      name: 'Art Only',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.ART_ONLY &&
        sku.artworkRequired &&
        !sku.requireCustomerArtworkApproval,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Artwork Needed';
        const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
        await lineItemRepo.save(lineItem);
        // await queueRepo.save({
        //   queueName,
        //   lineItem,
        // });
      },
    },
    // Workflow 3: Customer Image Only
    {
      name: 'Customer Image Only',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CUSTOMER_IMAGE_ONLY,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Ready for Vendor';
        await lineItemRepo.save(lineItem);
        // await queueRepo.save({
        //   queueName: 'Ready for Vendor',
        //   lineItem,
        // });
      },
    },
    // Workflow 4: Art + Template Placement
    {
      name: 'Art + Template Placement',
      condition: sku =>
        // sku.workflowCategory === WorkflowCategory.ART_TEMPLATE_PLACEMENT &&
        sku.artworkRequired && sku.requireTemplate,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Artwork Needed';
        const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
        await lineItemRepo.save(lineItem);
        // await queueRepo.save({
        //   queueName,
        //   lineItem,
        // });
      },
    },
    // Workflow 5: Generic Artwork Required
    {
      name: 'Generic Artwork Required',
      condition: sku => sku.artworkRequired,
      action: async (lineItem, sku, { lineItemRepo }) => {
        if (lineItem.status === 'Line Item Received') {
          lineItem.status = 'Artwork Needed';
          const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
          await lineItemRepo.save(lineItem);
          // await queueRepo.save({
          //   queueName,
          //   lineItem,
          // });
        }
      },
    },
  ];

  /**
   * Process CutoutPro items with cross-line-item deduplication
   */
  private async processCutoutProItemsWithDeduplication(
    items: Array<{ lineItem: LineItem; productSku: ProductSku }>
  ) {
    // Step 1: Collect all attachments from all line items
    const allAttachments: Attachment[] = [];
    const lineItemAttachmentMap = new Map<string, Attachment[]>();

    for (const { lineItem } of items) {
      if (lineItem.attachments && lineItem.attachments.length > 0) {
        allAttachments.push(...lineItem.attachments);
        lineItemAttachmentMap.set(lineItem.id, lineItem.attachments);
      }
    }

    // Step 2: Deduplicate across all attachments by SHA256 binary content
    const { uniqueAttachments, duplicateMap } = await this.deduplicateAttachmentsAcrossLineItems(allAttachments);
    console.log("uniqueAttachments", uniqueAttachments)
    console.log("duplicateMap", duplicateMap)

    // Step 3: Update line item statuses
    for (const { lineItem } of items) {
      console.log("lineitem***********", lineItem)
      await checkStatusTransition(lineItem.status, 'Cutout Pro Requested');
      lineItem.status = 'Cutout Pro Requested';
      await this.lineItemRepo.save(lineItem);
    }

    // Step 4: Process unique attachments with CutoutPro
    console.log("Starting CutoutPro processing...");
    for (const { lineItem, productSku } of items) {
      console.log("lineitem+++", lineItem)
      const cropType = productSku.cropType === CropType.FACE_CUTOUT ? '3' : '6';
      const lineItemAttachments = lineItemAttachmentMap.get(lineItem.id) || [];

      console.log(`Processing line item ${lineItem.itemNumber}: ${lineItemAttachments.length} total attachments`);

      // Only process attachments that are in the unique list
      const uniqueAttachmentsForThisLineItem = lineItemAttachments.filter(attachment =>
        uniqueAttachments.some(unique => unique.id === attachment.id)
      );

      console.log(`Unique attachments for ${lineItem.itemNumber}: ${uniqueAttachmentsForThisLineItem.length}`);
      console.log(`Unique attachment IDs:`, uniqueAttachmentsForThisLineItem.map(a => a.id));

      if (uniqueAttachmentsForThisLineItem.length > 0) {
        console.log(`Calling CutoutPro for line item ${lineItem.itemNumber} with ${uniqueAttachmentsForThisLineItem.length} attachments, cropType: ${cropType}`);
        await this.cutoutProApiService.process(lineItem, uniqueAttachmentsForThisLineItem, cropType);
        console.log(`CutoutPro processing completed for line item ${lineItem.itemNumber}`);
      } else {
        console.log(`No unique attachments to process for line item ${lineItem.itemNumber}`);
      }
    }

    // Step 5: Share CutoutPro URLs with duplicate attachments
    await this.shareCutoutProUrlsWithDuplicates(duplicateMap);
  }

  /**
   * Deduplicate attachments across all line items by SHA256 binary content
   */
  private async deduplicateAttachmentsAcrossLineItems(attachments: Attachment[]): Promise<{
    uniqueAttachments: Attachment[];
    duplicateMap: Map<string, Attachment[]>;
  }> {
    const hashMap = new Map<string, Attachment[]>();
    const uniqueAttachments: Attachment[] = [];
    const duplicateMap = new Map<string, Attachment[]>();

    // Calculate SHA256 hash for each attachment based on binary content
    for (const attachment of attachments) {
      try {
        const hash = await this.calculateImageHash(attachment.url);

        if (!hashMap.has(hash)) {
          hashMap.set(hash, []);
        }
        hashMap.get(hash)!.push(attachment);
      } catch (error) {
        console.error(`Failed to calculate hash for attachment ${attachment.id}:`, error);
        // If hash calculation fails, treat as unique
        uniqueAttachments.push(attachment);
      }
    }

    // Separate unique and duplicate attachments
    for (const [hash, attachmentGroup] of hashMap.entries()) {
      if (attachmentGroup.length === 1) {
        // Unique image
        uniqueAttachments.push(attachmentGroup[0]);
      } else {
        // Duplicate images - first one is original, rest are duplicates
        const [original, ...duplicates] = attachmentGroup;
        uniqueAttachments.push(original);
        duplicateMap.set(hash, duplicates);

        console.log(`Found ${duplicates.length} duplicate images for hash ${hash.substring(0, 8)}... (original: ${original.id})`);
      }
    }

    return { uniqueAttachments, duplicateMap };
  }

  /**
   * Share CutoutPro URLs from original attachments to their duplicates
   * This method will be called after CutoutPro processing is complete
   */
  private async shareCutoutProUrlsWithDuplicates(duplicateMap: Map<string, Attachment[]>) {
    // We'll implement a simpler approach:
    // After CutoutPro processing, find the processed attachments and share URLs

    for (const [hash, duplicates] of duplicateMap.entries()) {
      if (duplicates.length === 0) continue;

      // Find any attachment that has been processed with CutoutPro for this hash
      // We'll check all attachments and find one with cutoutProImageUrl that matches this hash
      let originalUrl: string | null = null;

      try {
        // Get all attachments that have been processed
        const processedAttachments = await this.attachmentRepo
          .createQueryBuilder('attachment')
          .where('attachment.cutoutProImageUrl IS NOT NULL')
          .getMany();

        // Find the one that matches our hash
        for (const attachment of processedAttachments) {
          const attachmentHash = await this.calculateImageHash(attachment.url);
          if (attachmentHash === hash) {
            originalUrl = attachment.cutoutProImageUrl;
            break;
          }
        }

        // Apply the URL to all duplicates
        if (originalUrl) {
          for (const duplicate of duplicates) {
            duplicate.cutoutProImageUrl = originalUrl;
            duplicate.status = 'Cutout Pro Completed';
          }

          await this.attachmentRepo.save(duplicates);
          console.log(`Shared CutoutPro URL to ${duplicates.length} duplicate attachments for hash ${hash.substring(0, 8)}...`);
        }
      } catch (error) {
        console.error(`Error sharing CutoutPro URL for hash ${hash}:`, error);
      }
    }
  }

  /**
   * Calculate SHA256 hash of an image from its URL
   */
  private async calculateImageHash(imageUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('sha256');
      const client = imageUrl.startsWith('https:') ? https : http;

      const request = client.get(imageUrl, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }

        response.on('data', (chunk) => {
          hash.update(chunk);
        });

        response.on('end', () => {
          const hashValue = hash.digest('hex');
          resolve(hashValue);
        });

        response.on('error', (error) => {
          reject(error);
        });
      });

      request.on('error', (error) => {
        reject(error);
      });

      request.setTimeout(10000, () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  /**
   * Process multiple line items with cross-line-item image deduplication
   */
  async processMultipleLineItems(lineItemsWithSkus: Array<{ lineItem: LineItem; productSku: ProductSku | null }>) {
    // Separate CutoutPro items from others
    const cutoutProItems = lineItemsWithSkus.filter(({ productSku }) =>
      productSku && productSku.croppingMethod === 'CUTOUT_PRO'
    );

    const nonCutoutProItems = lineItemsWithSkus.filter(({ productSku }) =>
      productSku && productSku.croppingMethod !== 'CUTOUT_PRO'
    );

    // For debugging - let's also process items that have attachments but no productSku
    const itemsWithAttachmentsButNoSku = lineItemsWithSkus.filter(({ productSku, lineItem }) =>
      !productSku && lineItem.attachments && lineItem.attachments.length > 0
    );

    console.log(`CutoutPro items: ${cutoutProItems.length}`);
    console.log(`Non-CutoutPro items: ${nonCutoutProItems.length}`);
    console.log(`Items with attachments but no SKU: ${itemsWithAttachmentsButNoSku.length}`);

    // Debug: Check the filtering logic
    for (const { lineItem, productSku } of lineItemsWithSkus) {
      console.log(`Line item ${lineItem.itemNumber}: productSku exists = ${!!productSku}, croppingMethod = ${productSku?.croppingMethod}`);
    }

    // Process CutoutPro items with cross-line-item deduplication
    if (cutoutProItems.length > 0) {
      console.log("About to call processCutoutProItemsWithDeduplication...");
      await this.processCutoutProItemsWithDeduplication(cutoutProItems as Array<{ lineItem: LineItem; productSku: ProductSku }>);
      console.log("Finished processCutoutProItemsWithDeduplication");
    } else {
      console.log("No CutoutPro items to process!");
    }

    // Process non-CutoutPro items normally
    for (const { lineItem, productSku } of nonCutoutProItems) {
      if (productSku) {
        await this.processLineItem(lineItem, productSku);
      }
    }

    // Don't process items without productSku - they need valid SKU from Shopify
    if (itemsWithAttachmentsButNoSku.length > 0) {
      console.log(`Skipping ${itemsWithAttachmentsButNoSku.length} items without valid productSku`);
    }
  }



  async processLineItem(lineItem: LineItem, productSku: ProductSku) {
    const services: WorkflowServices = {
      lineItemRepo: this.lineItemRepo,
      queueRepo: this.queueRepo,
      attachmentRepo: this.attachmentRepo,
      cutoutProApiService: this.cutoutProApiService,
    };

    // Set initial status
    if (!lineItem.status) {
      lineItem.status = 'Line Item Received';
      await this.lineItemRepo.save(lineItem);
    }

    // Apply workflows
    for (const workflow of this.workflows) {
      if (workflow.condition(productSku)) {
        await workflow.action(lineItem, productSku, services);
      }
    }
  }
}
