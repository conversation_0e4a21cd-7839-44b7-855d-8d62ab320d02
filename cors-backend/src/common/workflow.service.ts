import { Injectable } from '@nestjs/common';
import { Repository, In } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { CutoutProService } from './cutout-pro.service';
import * as crypto from 'crypto';
import * as https from 'https';
import * as http from 'http';

import {
  CroppingMethod,
  WorkflowCategory,
  CropType,
} from 'src/product-sku/enums/product-sku.enums';
import { checkStatusTransition } from 'src/utils/workflow.utils';
import { DBHelper } from 'src/helpers/db.helpers';

interface WorkflowRule {
  name: string;
  condition: (sku: ProductSku) => boolean;
  action: (
    lineItem: LineItem,
    sku: ProductSku,
    services: WorkflowServices,
  ) => Promise<void>;
}

interface WorkflowServices {
  lineItemRepo: Repository<LineItem>;
  queueRepo: Repository<Queue>;
  attachmentRepo: Repository<Attachment>;
  cutoutProApiService: CutoutProService;
}

@Injectable()
export class WorkflowService {
  constructor(
    @InjectRepository(LineItem)
    private lineItemRepo: Repository<LineItem>,
    @InjectRepository(Queue)
    private queueRepo: Repository<Queue>,
    @InjectRepository(Attachment)
    private attachmentRepo: Repository<Attachment>,
    private cutoutProApiService: CutoutProService,
  ) {}

  private workflows: WorkflowRule[] = [
    // Workflow 1: Crop Image Only or Crop Image and Template Placement
    {
      name: 'Crop Image Only or Crop Image and Template Placement',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_ONLY ||
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_AND_TEMPLATE,
      action: async (
        lineItem,
        sku,
        { lineItemRepo, queueRepo, attachmentRepo, cutoutProApiService },
      ) => {
        if (sku.croppingMethod === CroppingMethod.CUTOUT_PRO) {
          await checkStatusTransition(lineItem.status, 'Cutout Pro Requested');
          lineItem.status = 'Cutout Pro Requested';
          await lineItemRepo.save(lineItem);
          const attachments = lineItem.attachments;
          const cropType = sku.cropType === CropType.FACE_CUTOUT ? '3' : '6';

          // Simple SHA256 deduplication within this line item
          const uniqueAttachments = await this.deduplicateAttachments(attachments);
          console.log(`Line item ${lineItem.itemNumber}: ${attachments.length} total -> ${uniqueAttachments.length} unique attachments`);

          await cutoutProApiService.process(lineItem, uniqueAttachments, cropType);
        } else if (sku.croppingMethod === CroppingMethod.MANUAL) {
          let cropNeededQueue = await DBHelper.findOne(queueRepo, {
            where: { name: 'Crop Needed' },
          });
          if (!cropNeededQueue) {
            cropNeededQueue = queueRepo.create({
              name: 'Crop Needed',
            });
            cropNeededQueue = await queueRepo.save(cropNeededQueue);
          }
          const attachments = lineItem.attachments;
          for (const attachment of attachments) {
            attachment.status = 'Manual Crop Needed';
            attachment.queue = cropNeededQueue;
          }
          await checkStatusTransition(lineItem.status, 'Crop Needed');
          await attachmentRepo.save(attachments);
          lineItem.status = 'Crop Needed';
          await lineItemRepo.save(lineItem);
        }
      },
    },
    // Workflow 2: Art Only
    {
      name: 'Art Only',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.ART_ONLY &&
        sku.artworkRequired &&
        !sku.requireCustomerArtworkApproval,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Artwork Needed';
        const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
        await lineItemRepo.save(lineItem);
      },
    },
    // Workflow 3: Customer Image Only
    {
      name: 'Customer Image Only',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CUSTOMER_IMAGE_ONLY,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Ready for Vendor';
        await lineItemRepo.save(lineItem);
      },
    },
    // Workflow 4: Art + Template Placement
    {
      name: 'Art + Template Placement',
      condition: sku =>
        sku.artworkRequired && sku.requireTemplate,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Artwork Needed';
        const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
        await lineItemRepo.save(lineItem);
      },
    },
    // Workflow 5: Generic Artwork Required
    {
      name: 'Generic Artwork Required',
      condition: sku => sku.artworkRequired,
      action: async (lineItem, sku, { lineItemRepo }) => {
        if (lineItem.status === 'Line Item Received') {
          lineItem.status = 'Artwork Needed';
          const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
          await lineItemRepo.save(lineItem);
        }
      },
    },
  ];

  /**
   * Simple SHA256 deduplication for attachments within a line item
   */
  private async deduplicateAttachments(attachments: Attachment[]): Promise<Attachment[]> {
    if (!attachments || attachments.length === 0) {
      return [];
    }

    const hashMap = new Map<string, Attachment>();
    const uniqueAttachments: Attachment[] = [];

    for (const attachment of attachments) {
      try {
        const hash = await this.calculateImageHash(attachment.url);

        if (!hashMap.has(hash)) {
          // First occurrence of this hash - keep it
          hashMap.set(hash, attachment);
          uniqueAttachments.push(attachment);
        } else {
          // Duplicate found - we'll share the CutoutPro URL later
          console.log(`Duplicate image found for attachment ${attachment.id}, will share URL from original`);
        }
      } catch (error) {
        console.error(`Failed to calculate hash for attachment ${attachment.id}:`, error);
        // If hash calculation fails, treat as unique
        uniqueAttachments.push(attachment);
      }
    }

    return uniqueAttachments;
  }

  /**
   * Calculate SHA256 hash of an image from its URL
   */
  private async calculateImageHash(imageUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('sha256');
      const client = imageUrl.startsWith('https:') ? https : http;

      const request = client.get(imageUrl, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }

        response.on('data', (chunk) => {
          hash.update(chunk);
        });

        response.on('end', () => {
          const hashValue = hash.digest('hex');
          resolve(hashValue);
        });

        response.on('error', (error) => {
          reject(error);
        });
      });

      request.on('error', (error) => {
        reject(error);
      });

      request.setTimeout(10000, () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  // Static map to track processed images across all line items in an order
  private static processedImages = new Map<string, string>(); // hash -> cutoutProUrl
  private static pendingLineItems = new Map<string, Array<{ lineItem: LineItem; productSku: ProductSku }>>(); // orderId -> line items

  async processLineItem(lineItem: LineItem, productSku: ProductSku) {
    const services: WorkflowServices = {
      lineItemRepo: this.lineItemRepo,
      queueRepo: this.queueRepo,
      attachmentRepo: this.attachmentRepo,
      cutoutProApiService: this.cutoutProApiService,
    };

    // Set initial status
    if (!lineItem.status) {
      lineItem.status = 'Line Item Received';
      await this.lineItemRepo.save(lineItem);
    }

    // For CutoutPro items, use cross-line-item deduplication
    if (productSku.croppingMethod === CroppingMethod.CUTOUT_PRO) {
      await this.processCutoutProWithDeduplication(lineItem, productSku, services);
    } else {
      // Apply normal workflows for non-CutoutPro items
      for (const workflow of this.workflows) {
        if (workflow.condition(productSku)) {
          await workflow.action(lineItem, productSku, services);
        }
      }
    }
  }

  private async processCutoutProWithDeduplication(
    lineItem: LineItem,
    productSku: ProductSku,
    services: WorkflowServices
  ) {
    const orderId = lineItem.order.id;

    // Add this line item to pending list for this order
    if (!WorkflowService.pendingLineItems.has(orderId)) {
      WorkflowService.pendingLineItems.set(orderId, []);
    }
    WorkflowService.pendingLineItems.get(orderId)!.push({ lineItem, productSku });

    console.log(`Added line item ${lineItem.itemNumber} to pending list for order ${orderId}`);

    // Process immediately (we'll handle deduplication in the workflow action)
    await checkStatusTransition(lineItem.status, 'Cutout Pro Requested');
    lineItem.status = 'Cutout Pro Requested';
    await services.lineItemRepo.save(lineItem);

    const attachments = lineItem.attachments || [];
    const cropType = productSku.cropType === CropType.FACE_CUTOUT ? '3' : '6';

    // Process each attachment with cross-line-item deduplication
    const uniqueAttachments: Attachment[] = [];
    const duplicateAttachments: Attachment[] = [];

    for (const attachment of attachments) {
      try {
        const hash = await this.calculateImageHash(attachment.url);

        // Check if we've already processed this image
        if (WorkflowService.processedImages.has(hash)) {
          // This is a duplicate - assign the existing URL
          const existingUrl = WorkflowService.processedImages.get(hash)!;
          attachment.cutoutProImageUrl = existingUrl;
          attachment.status = 'Cutout Pro Completed';
          duplicateAttachments.push(attachment);
          console.log(`Duplicate found: ${attachment.id} -> using existing URL from hash ${hash.substring(0, 8)}...`);
        } else {
          // This is unique - needs to be processed
          uniqueAttachments.push(attachment);
        }
      } catch (error) {
        console.error(`Failed to calculate hash for attachment ${attachment.id}:`, error);
        // If hash calculation fails, treat as unique
        uniqueAttachments.push(attachment);
      }
    }

    console.log(`Line item ${lineItem.itemNumber}: ${attachments.length} total -> ${uniqueAttachments.length} unique, ${duplicateAttachments.length} duplicates`);

    // Save duplicate attachments with their URLs
    if (duplicateAttachments.length > 0) {
      await services.attachmentRepo.save(duplicateAttachments);
    }

    // Process unique attachments with CutoutPro
    if (uniqueAttachments.length > 0) {
      await services.cutoutProApiService.process(lineItem, uniqueAttachments, cropType);

      // After processing, store the URLs for future duplicates
      for (const attachment of uniqueAttachments) {
        if (attachment.cutoutProImageUrl) {
          try {
            const hash = await this.calculateImageHash(attachment.url);
            WorkflowService.processedImages.set(hash, attachment.cutoutProImageUrl);
            console.log(`Stored URL for hash ${hash.substring(0, 8)}...: ${attachment.cutoutProImageUrl}`);
          } catch (error) {
            console.error(`Failed to store URL for attachment ${attachment.id}:`, error);
          }
        }
      }
    }
  }

  /**
   * Clear processed images for an order (call this when order processing is complete)
   */
  static clearOrderCache(orderId: string) {
    WorkflowService.pendingLineItems.delete(orderId);
    // Note: We keep processedImages across orders to maximize deduplication
    // If you want to clear per order, you can modify this logic
    console.log(`Cleared cache for order ${orderId}`);
  }

  /**
   * Clear all processed images (call this periodically or when needed)
   */
  static clearAllCache() {
    WorkflowService.processedImages.clear();
    WorkflowService.pendingLineItems.clear();
    console.log('Cleared all image cache');
  }
}