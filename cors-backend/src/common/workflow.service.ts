import { Injectable } from '@nestjs/common';
import { Repository, In } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { CutoutProService } from './cutout-pro.service';
import * as crypto from 'crypto';
import * as https from 'https';
import * as http from 'http';

import {
  CroppingMethod,
  WorkflowCategory,
  CropType,
} from 'src/product-sku/enums/product-sku.enums';
import { checkStatusTransition } from 'src/utils/workflow.utils';
import { DBHelper } from 'src/helpers/db.helpers';

interface WorkflowRule {
  name: string;
  condition: (sku: ProductSku) => boolean;
  action: (
    lineItem: LineItem,
    sku: ProductSku,
    services: WorkflowServices,
  ) => Promise<void>;
}

interface WorkflowServices {
  lineItemRepo: Repository<LineItem>;
  queueRepo: Repository<Queue>;
  attachmentRepo: Repository<Attachment>;
  cutoutProApiService: CutoutProService;
}

@Injectable()
export class WorkflowService {
  constructor(
    @InjectRepository(LineItem)
    private lineItemRepo: Repository<LineItem>,
    @InjectRepository(Queue)
    private queueRepo: Repository<Queue>,
    @InjectRepository(Attachment)
    private attachmentRepo: Repository<Attachment>,
    private cutoutProApiService: CutoutProService,
  ) {}

  private workflows: WorkflowRule[] = [
    // Workflow 1: Crop Image Only or Crop Image and Template Placement
    {
      name: 'Crop Image Only or Crop Image and Template Placement',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_ONLY ||
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_AND_TEMPLATE,
      action: async (
        lineItem,
        sku,
        { lineItemRepo, queueRepo, attachmentRepo, cutoutProApiService },
      ) => {
        if (sku.croppingMethod === CroppingMethod.CUTOUT_PRO) {
          await checkStatusTransition(lineItem.status, 'Cutout Pro Requested');
          lineItem.status = 'Cutout Pro Requested';
          await lineItemRepo.save(lineItem);
          const attachments = lineItem.attachments;
          const cropType = sku.cropType === CropType.FACE_CUTOUT ? '3' : '6';

          // Simple SHA256 deduplication within this line item
          const uniqueAttachments = await this.deduplicateAttachments(attachments);
          console.log(`Line item ${lineItem.itemNumber}: ${attachments.length} total -> ${uniqueAttachments.length} unique attachments`);

          await cutoutProApiService.process(lineItem, uniqueAttachments, cropType);
        } else if (sku.croppingMethod === CroppingMethod.MANUAL) {
          let cropNeededQueue = await DBHelper.findOne(queueRepo, {
            where: { name: 'Crop Needed' },
          });
          if (!cropNeededQueue) {
            cropNeededQueue = queueRepo.create({
              name: 'Crop Needed',
            });
            cropNeededQueue = await queueRepo.save(cropNeededQueue);
          }
          const attachments = lineItem.attachments;
          for (const attachment of attachments) {
            attachment.status = 'Manual Crop Needed';
            attachment.queue = cropNeededQueue;
          }
          await checkStatusTransition(lineItem.status, 'Crop Needed');
          await attachmentRepo.save(attachments);
          lineItem.status = 'Crop Needed';
          await lineItemRepo.save(lineItem);
        }
      },
    },
    // Workflow 2: Art Only
    {
      name: 'Art Only',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.ART_ONLY &&
        sku.artworkRequired &&
        !sku.requireCustomerArtworkApproval,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Artwork Needed';
        const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
        await lineItemRepo.save(lineItem);
      },
    },
    // Workflow 3: Customer Image Only
    {
      name: 'Customer Image Only',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CUSTOMER_IMAGE_ONLY,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Ready for Vendor';
        await lineItemRepo.save(lineItem);
      },
    },
    // Workflow 4: Art + Template Placement
    {
      name: 'Art + Template Placement',
      condition: sku =>
        sku.artworkRequired && sku.requireTemplate,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Artwork Needed';
        const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
        await lineItemRepo.save(lineItem);
      },
    },
    // Workflow 5: Generic Artwork Required
    {
      name: 'Generic Artwork Required',
      condition: sku => sku.artworkRequired,
      action: async (lineItem, sku, { lineItemRepo }) => {
        if (lineItem.status === 'Line Item Received') {
          lineItem.status = 'Artwork Needed';
          const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
          await lineItemRepo.save(lineItem);
        }
      },
    },
  ];

  /**
   * Simple SHA256 deduplication for attachments within a line item
   */
  private async deduplicateAttachments(attachments: Attachment[]): Promise<Attachment[]> {
    if (!attachments || attachments.length === 0) {
      return [];
    }

    const hashMap = new Map<string, Attachment>();
    const uniqueAttachments: Attachment[] = [];

    for (const attachment of attachments) {
      try {
        const hash = await this.calculateImageHash(attachment.url);

        if (!hashMap.has(hash)) {
          // First occurrence of this hash - keep it
          hashMap.set(hash, attachment);
          uniqueAttachments.push(attachment);
        } else {
          // Duplicate found - we'll share the CutoutPro URL later
          console.log(`Duplicate image found for attachment ${attachment.id}, will share URL from original`);
        }
      } catch (error) {
        console.error(`Failed to calculate hash for attachment ${attachment.id}:`, error);
        // If hash calculation fails, treat as unique
        uniqueAttachments.push(attachment);
      }
    }

    return uniqueAttachments;
  }

  /**
   * Calculate SHA256 hash of an image from its URL
   */
  private async calculateImageHash(imageUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('sha256');
      const client = imageUrl.startsWith('https:') ? https : http;

      const request = client.get(imageUrl, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }

        response.on('data', (chunk) => {
          hash.update(chunk);
        });

        response.on('end', () => {
          const hashValue = hash.digest('hex');
          resolve(hashValue);
        });

        response.on('error', (error) => {
          reject(error);
        });
      });

      request.on('error', (error) => {
        reject(error);
      });

      request.setTimeout(10000, () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  // Static map to track order processing
  private static orderProcessingStatus = new Map<string, {
    lineItems: Array<{ lineItem: LineItem; productSku: ProductSku }>;
    totalExpected: number;
    processed: number;
  }>();

  async processLineItem(lineItem: LineItem, productSku: ProductSku) {
    console.log(`\n=== Processing Line Item ${lineItem.itemNumber} ===`);
    console.log(`Order ID: ${lineItem.order.id}`);
    console.log(`Product SKU: ${productSku.sku}`);
    console.log(`Cropping Method: ${productSku.croppingMethod}`);
    console.log(`Attachments: ${lineItem.attachments?.length || 0}`);

    const services: WorkflowServices = {
      lineItemRepo: this.lineItemRepo,
      queueRepo: this.queueRepo,
      attachmentRepo: this.attachmentRepo,
      cutoutProApiService: this.cutoutProApiService,
    };

    // Set initial status
    if (!lineItem.status) {
      lineItem.status = 'Line Item Received';
      await this.lineItemRepo.save(lineItem);
      console.log(`Set initial status: Line Item Received`);
    }

    // For CutoutPro items, collect them for order-level processing
    if (productSku.croppingMethod === CroppingMethod.CUTOUT_PRO) {
      await this.collectCutoutProLineItem(lineItem, productSku, services);
    } else {
      console.log(`Processing non-CutoutPro item normally`);
      // Apply normal workflows for non-CutoutPro items
      for (const workflow of this.workflows) {
        if (workflow.condition(productSku)) {
          await workflow.action(lineItem, productSku, services);
        }
      }
    }
  }

  /**
   * Collect CutoutPro line items and process them when we have all line items for the order
   */
  private async collectCutoutProLineItem(
    lineItem: LineItem,
    productSku: ProductSku,
    services: WorkflowServices
  ) {
    const orderId = lineItem.order.id;
    console.log(`\n--- Collecting CutoutPro Line Item for Order ${orderId} ---`);

    // Initialize order tracking if not exists
    if (!WorkflowService.orderProcessingStatus.has(orderId)) {
      // Get total line items for this order
      const totalLineItems = await services.lineItemRepo.count({
        where: { order: { id: orderId } }
      });

      WorkflowService.orderProcessingStatus.set(orderId, {
        lineItems: [],
        totalExpected: totalLineItems,
        processed: 0
      });

      console.log(`Initialized order tracking: expecting ${totalLineItems} line items`);
    }

    const orderStatus = WorkflowService.orderProcessingStatus.get(orderId)!;
    orderStatus.lineItems.push({ lineItem, productSku });
    orderStatus.processed++;

    console.log(`Collected line item ${lineItem.itemNumber} (${orderStatus.processed}/${orderStatus.totalExpected})`);

    // Check if we have all line items for this order
    if (orderStatus.processed >= orderStatus.totalExpected) {
      console.log(`\n🚀 All line items collected for order ${orderId}. Starting cross-line-item deduplication...`);
      await this.processOrderWithDeduplication(orderId, orderStatus.lineItems, services);

      // Clean up
      WorkflowService.orderProcessingStatus.delete(orderId);
      console.log(`Cleaned up order tracking for ${orderId}`);
    } else {
      console.log(`Waiting for more line items... (${orderStatus.processed}/${orderStatus.totalExpected})`);
    }
  }

  /**
   * Process all line items for an order with cross-line-item deduplication
   */
  private async processOrderWithDeduplication(
    orderId: string,
    lineItemsWithSkus: Array<{ lineItem: LineItem; productSku: ProductSku }>,
    services: WorkflowServices
  ) {
    console.log(`\n🔍 Starting cross-line-item deduplication for order ${orderId}`);

    // Step 1: Collect ALL attachments from ALL line items
    const allAttachments: Attachment[] = [];
    const lineItemAttachmentMap = new Map<string, Attachment[]>();

    for (const { lineItem } of lineItemsWithSkus) {
      if (lineItem.attachments && lineItem.attachments.length > 0) {
        allAttachments.push(...lineItem.attachments);
        lineItemAttachmentMap.set(lineItem.id, lineItem.attachments);
        console.log(`Line item ${lineItem.itemNumber}: ${lineItem.attachments.length} attachments`);
      }
    }

    console.log(`\n📊 Total attachments across all line items: ${allAttachments.length}`);

    // Step 2: Calculate SHA256 hashes and find duplicates
    const { uniqueAttachments, duplicateMap } = await this.findDuplicatesByHash(allAttachments);

    console.log(`\n✨ Deduplication Results:`);
    console.log(`- Unique attachments: ${uniqueAttachments.length}`);
    console.log(`- Duplicate groups: ${duplicateMap.size}`);
    console.log(`- Total duplicates: ${Array.from(duplicateMap.values()).reduce((sum, arr) => sum + arr.length, 0)}`);
    console.log(`- CutoutPro API calls saved: ${allAttachments.length - uniqueAttachments.length}`);

    // Step 3: Update line item statuses
    for (const { lineItem } of lineItemsWithSkus) {
      await checkStatusTransition(lineItem.status, 'Cutout Pro Requested');
      lineItem.status = 'Cutout Pro Requested';
      await services.lineItemRepo.save(lineItem);
      console.log(`Updated status for line item ${lineItem.itemNumber}: Cutout Pro Requested`);
    }

    // Step 4: Process unique attachments with CutoutPro
    console.log(`\n🔄 Processing ${uniqueAttachments.length} unique attachments with CutoutPro...`);

    for (const { lineItem, productSku } of lineItemsWithSkus) {
      const cropType = productSku.cropType === CropType.FACE_CUTOUT ? '3' : '6';
      const lineItemAttachments = lineItemAttachmentMap.get(lineItem.id) || [];

      // Filter to only unique attachments for this line item
      const uniqueAttachmentsForThisLineItem = lineItemAttachments.filter(attachment =>
        uniqueAttachments.some(unique => unique.id === attachment.id)
      );

      if (uniqueAttachmentsForThisLineItem.length > 0) {
        console.log(`Processing ${uniqueAttachmentsForThisLineItem.length} unique attachments for line item ${lineItem.itemNumber}`);
        await services.cutoutProApiService.process(lineItem, uniqueAttachmentsForThisLineItem, cropType);
        console.log(`✅ CutoutPro processing completed for line item ${lineItem.itemNumber}`);
      } else {
        console.log(`No unique attachments to process for line item ${lineItem.itemNumber} (all were duplicates)`);
      }
    }

    // Step 5: Share CutoutPro URLs with duplicate attachments
    console.log(`\n🔗 Sharing CutoutPro URLs with duplicate attachments...`);
    await this.shareCutoutProUrlsWithDuplicates(duplicateMap, services);

    console.log(`\n🎉 Order ${orderId} processing completed!`);
  }

  /**
   * Find duplicates by calculating SHA256 hash for each attachment
   */
  private async findDuplicatesByHash(attachments: Attachment[]): Promise<{
    uniqueAttachments: Attachment[];
    duplicateMap: Map<string, Attachment[]>;
  }> {
    console.log(`\n🔍 Calculating SHA256 hashes for ${attachments.length} attachments...`);

    const hashMap = new Map<string, Attachment[]>();
    const uniqueAttachments: Attachment[] = [];
    const duplicateMap = new Map<string, Attachment[]>();

    // Calculate SHA256 hash for each attachment
    for (let i = 0; i < attachments.length; i++) {
      const attachment = attachments[i];
      console.log(`Processing attachment ${i + 1}/${attachments.length}: ${attachment.id}`);

      try {
        const hash = await this.calculateImageHash(attachment.url);
        console.log(`  Hash: ${hash.substring(0, 16)}...`);

        if (!hashMap.has(hash)) {
          hashMap.set(hash, []);
        }
        hashMap.get(hash)!.push(attachment);
      } catch (error) {
        console.error(`  ❌ Failed to calculate hash: ${error.message}`);
        // If hash calculation fails, treat as unique
        uniqueAttachments.push(attachment);
      }
    }

    // Separate unique and duplicate attachments
    console.log(`\n📋 Analyzing hash groups...`);
    for (const [hash, attachmentGroup] of hashMap.entries()) {
      if (attachmentGroup.length === 1) {
        // Unique image
        uniqueAttachments.push(attachmentGroup[0]);
        console.log(`  Unique: ${hash.substring(0, 8)}... (${attachmentGroup[0].id})`);
      } else {
        // Duplicate images - first one is original, rest are duplicates
        const [original, ...duplicates] = attachmentGroup;
        uniqueAttachments.push(original);
        duplicateMap.set(hash, duplicates);

        console.log(`  🔄 Duplicates found for ${hash.substring(0, 8)}...:`);
        console.log(`    Original: ${original.id}`);
        console.log(`    Duplicates: ${duplicates.map(d => d.id).join(', ')}`);
      }
    }

    return { uniqueAttachments, duplicateMap };
  }

  /**
   * Share CutoutPro URLs with duplicate attachments
   */
  private async shareCutoutProUrlsWithDuplicates(
    duplicateMap: Map<string, Attachment[]>,
    services: WorkflowServices
  ) {
    for (const [hash, duplicates] of duplicateMap.entries()) {
      if (duplicates.length === 0) continue;

      console.log(`\n🔗 Processing duplicates for hash ${hash.substring(0, 8)}...`);

      try {
        // Find the original attachment that was processed
        const processedAttachments = await services.attachmentRepo
          .createQueryBuilder('attachment')
          .where('attachment.cutoutProImageUrl IS NOT NULL')
          .getMany();

        // Find the one that matches our hash
        let originalUrl: string | null = null;
        for (const attachment of processedAttachments) {
          const attachmentHash = await this.calculateImageHash(attachment.url);
          if (attachmentHash === hash) {
            originalUrl = attachment.cutoutProImageUrl;
            console.log(`  Found original URL: ${originalUrl}`);
            break;
          }
        }

        // Apply the URL to all duplicates
        if (originalUrl) {
          for (const duplicate of duplicates) {
            duplicate.cutoutProImageUrl = originalUrl;
            duplicate.status = 'Cutout Pro Completed';
            console.log(`  ✅ Applied URL to duplicate: ${duplicate.id}`);
          }

          await services.attachmentRepo.save(duplicates);
          console.log(`  💾 Saved ${duplicates.length} duplicate attachments`);
        } else {
          console.log(`  ❌ No original URL found for hash ${hash.substring(0, 8)}...`);
        }
      } catch (error) {
        console.error(`  ❌ Error sharing URL for hash ${hash}: ${error.message}`);
      }
    }
  }

  /**
   * Clear order cache (call this when order processing is complete)
   */
  static clearOrderCache(orderId: string) {
    WorkflowService.orderProcessingStatus.delete(orderId);
    console.log(`Cleared cache for order ${orderId}`);
  }

  /**
   * Clear all cache (call this periodically or when needed)
   */
  static clearAllCache() {
    WorkflowService.orderProcessingStatus.clear();
    console.log('Cleared all order cache');
  }
}