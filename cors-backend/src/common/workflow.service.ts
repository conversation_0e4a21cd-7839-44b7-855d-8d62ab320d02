import { Injectable } from '@nestjs/common';
import { Repository, In } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { CutoutProService } from './cutout-pro.service';
import { ImageDeduplicationService } from './image-deduplication.service';

import {
  CroppingMethod,
  WorkflowCategory,
  CropType,
} from 'src/product-sku/enums/product-sku.enums';
import { checkStatusTransition } from 'src/utils/workflow.utils';
import { DBHelper } from 'src/helpers/db.helpers';
interface WorkflowRule {
  name: string;
  condition: (sku: ProductSku) => boolean;
  action: (
    lineItem: LineItem,
    sku: ProductSku,
    services: WorkflowServices,
  ) => Promise<void>;
}

interface WorkflowServices {
  lineItemRepo: Repository<LineItem>;
  queueRepo: Repository<Queue>;
  attachmentRepo: Repository<Attachment>;
  cutoutProApiService: CutoutProService;
  imageDeduplicationService: ImageDeduplicationService;
}

@Injectable()
export class WorkflowService {
  constructor(
    @InjectRepository(LineItem)
    private lineItemRepo: Repository<LineItem>,
    @InjectRepository(Queue)
    private queueRepo: Repository<Queue>,
    @InjectRepository(Attachment)
    private attachmentRepo: Repository<Attachment>,
    private cutoutProApiService: CutoutProService,
    private imageDeduplicationService: ImageDeduplicationService,
  ) {}

  private workflows: WorkflowRule[] = [
    // Workflow 1: Crop Image Only or Crop Image and Template Placement
    {
      name: 'Crop Image Only or Crop Image and Template Placement',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_ONLY ||
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_AND_TEMPLATE,
      action: async (
        lineItem,
        sku,
        { lineItemRepo, queueRepo, attachmentRepo, cutoutProApiService, imageDeduplicationService },
      ) => {
        if (sku.croppingMethod === CroppingMethod.CUTOUT_PRO) {
          await checkStatusTransition(lineItem.status, 'Cutout Pro Requested');
          lineItem.status = 'Cutout Pro Requested';
          await lineItemRepo.save(lineItem);

          // Perform image deduplication within this line item
          const deduplicationResult = await imageDeduplicationService.deduplicateWithinLineItem(lineItem);

          // Only process unique attachments with CutoutPro
          const cropType = sku.cropType === CropType.FACE_CUTOUT ? '3' : '6';

          // Process each unique attachment
          for (const attachment of deduplicationResult.uniqueAttachments) {
            try {
              // Process with CutoutPro
              await cutoutProApiService.process(lineItem, [attachment], cropType);

              // Reload the attachment to get the updated cutoutProImageUrl
              const updatedAttachment = await attachmentRepo.findOne({
                where: { id: attachment.id }
              });

              if (updatedAttachment?.cutoutProImageUrl) {
                // Get the hash for this attachment to find its duplicates
                const attachmentHash = Array.from(deduplicationResult.originalMap.entries())
                  .find(([_, original]) => original.id === attachment.id)?.[0];

                if (attachmentHash && deduplicationResult.duplicateMap.has(attachmentHash)) {
                  const duplicates = deduplicationResult.duplicateMap.get(attachmentHash)!;
                  // Apply the CutoutPro URL to all duplicates
                  await imageDeduplicationService.applyUrlToDuplicates(
                    updatedAttachment.cutoutProImageUrl,
                    updatedAttachment,
                    duplicates
                  );
                }
              }
            } catch (error) {
              console.error(`Failed to process attachment ${attachment.id}:`, error);
            }
          }
        } else if (sku.croppingMethod === CroppingMethod.MANUAL) {
          let cropNeededQueue = await DBHelper.findOne(queueRepo, {
            where: { name: 'Crop Needed' },
          });
          if (!cropNeededQueue) {
            cropNeededQueue = queueRepo.create({
              name: 'Crop Needed',
            });
            cropNeededQueue = await queueRepo.save(cropNeededQueue);
          }
          const attachments = lineItem.attachments;
          for (const attachment of attachments) {
            attachment.status = 'Manual Crop Needed';
            attachment.queue = cropNeededQueue;
          }
          await checkStatusTransition(lineItem.status, 'Crop Needed');
          await attachmentRepo.save(attachments);
          lineItem.status = 'Crop Needed';
          await lineItemRepo.save(lineItem);
        }
      },
    },
    // Workflow 2: Art Only
    {
      name: 'Art Only',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.ART_ONLY &&
        sku.artworkRequired &&
        !sku.requireCustomerArtworkApproval,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Artwork Needed';
        const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
        await lineItemRepo.save(lineItem);
        // await queueRepo.save({
        //   queueName,
        //   lineItem,
        // });
      },
    },
    // Workflow 3: Customer Image Only
    {
      name: 'Customer Image Only',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CUSTOMER_IMAGE_ONLY,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Ready for Vendor';
        await lineItemRepo.save(lineItem);
        // await queueRepo.save({
        //   queueName: 'Ready for Vendor',
        //   lineItem,
        // });
      },
    },
    // Workflow 4: Art + Template Placement
    {
      name: 'Art + Template Placement',
      condition: sku =>
        // sku.workflowCategory === WorkflowCategory.ART_TEMPLATE_PLACEMENT &&
        sku.artworkRequired && sku.requireTemplate,
      action: async (lineItem, sku, { lineItemRepo }) => {
        lineItem.status = 'Artwork Needed';
        const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
        await lineItemRepo.save(lineItem);
        // await queueRepo.save({
        //   queueName,
        //   lineItem,
        // });
      },
    },
    // Workflow 5: Generic Artwork Required
    {
      name: 'Generic Artwork Required',
      condition: sku => sku.artworkRequired,
      action: async (lineItem, sku, { lineItemRepo }) => {
        if (lineItem.status === 'Line Item Received') {
          lineItem.status = 'Artwork Needed';
          const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
          await lineItemRepo.save(lineItem);
          // await queueRepo.save({
          //   queueName,
          //   lineItem,
          // });
        }
      },
    },
  ];

  /**
   * Process multiple line items with cross-line-item image deduplication
   */
  async processMultipleLineItems(lineItemsWithSkus: Array<{ lineItem: LineItem; productSku: ProductSku }>) {
    const services: WorkflowServices = {
      lineItemRepo: this.lineItemRepo,
      queueRepo: this.queueRepo,
      attachmentRepo: this.attachmentRepo,
      cutoutProApiService: this.cutoutProApiService,
      imageDeduplicationService: this.imageDeduplicationService,
    };

    // Extract all line items for deduplication
    const allLineItems = lineItemsWithSkus.map(item => item.lineItem);

    // Perform cross-line-item deduplication
    const deduplicationResult = await this.imageDeduplicationService.deduplicateAcrossLineItems(allLineItems);

    // Group line items by workflow category for batch processing
    const workflowGroups = new Map<string, Array<{ lineItem: LineItem; productSku: ProductSku }>>();

    for (const item of lineItemsWithSkus) {
      const { lineItem, productSku } = item;

      // Set initial status
      if (!lineItem.status) {
        lineItem.status = 'Line Item Received';
        await this.lineItemRepo.save(lineItem);
      }

      // Group by workflow category
      const workflowKey = this.getWorkflowKey(productSku);
      if (!workflowGroups.has(workflowKey)) {
        workflowGroups.set(workflowKey, []);
      }
      workflowGroups.get(workflowKey)!.push(item);
    }

    // Process each workflow group
    for (const [workflowKey, items] of workflowGroups.entries()) {
      await this.processWorkflowGroup(items, deduplicationResult, services);
    }
  }

  private getWorkflowKey(productSku: ProductSku): string {
    return `${productSku.workflowCategory}_${productSku.croppingMethod}_${productSku.cropType}`;
  }

  private async processWorkflowGroup(
    items: Array<{ lineItem: LineItem; productSku: ProductSku }>,
    deduplicationResult: any,
    services: WorkflowServices
  ) {
    // For CutoutPro workflows, we need special handling for deduplication
    const cutoutProItems = items.filter(({ productSku }) =>
      productSku.croppingMethod === CroppingMethod.CUTOUT_PRO
    );

    const nonCutoutProItems = items.filter(({ productSku }) =>
      productSku.croppingMethod !== CroppingMethod.CUTOUT_PRO
    );

    // Process CutoutPro items with deduplication
    if (cutoutProItems.length > 0) {
      await this.processCutoutProItemsWithDeduplication(cutoutProItems, deduplicationResult, services);
    }

    // Process non-CutoutPro items normally
    for (const { lineItem, productSku } of nonCutoutProItems) {
      for (const workflow of this.workflows) {
        if (workflow.condition(productSku)) {
          await workflow.action(lineItem, productSku, services);
        }
      }
    }
  }

  private async processCutoutProItemsWithDeduplication(
    items: Array<{ lineItem: LineItem; productSku: ProductSku }>,
    deduplicationResult: any,
    services: WorkflowServices
  ) {
    const { cutoutProApiService, attachmentRepo, lineItemRepo, imageDeduplicationService } = services;

    // Process unique attachments only once
    const processedHashes = new Set<string>();

    for (const { lineItem, productSku } of items) {
      // Update line item status
      await checkStatusTransition(lineItem.status, 'Cutout Pro Requested');
      lineItem.status = 'Cutout Pro Requested';
      await lineItemRepo.save(lineItem);

      const cropType = productSku.cropType === CropType.FACE_CUTOUT ? '3' : '6';

      // Process attachments for this line item
      for (const attachment of lineItem.attachments || []) {
        const hash = attachment.imageHash;

        if (hash && !processedHashes.has(hash)) {
          // This is the first time we're processing this hash
          processedHashes.add(hash);

          try {
            // Process with CutoutPro
            await cutoutProApiService.process(lineItem, [attachment], cropType);

            // Reload the attachment to get the updated cutoutProImageUrl
            const updatedAttachment = await attachmentRepo.findOne({
              where: { id: attachment.id }
            });

            if (updatedAttachment?.cutoutProImageUrl && deduplicationResult.duplicateMap.has(hash)) {
              const duplicates = deduplicationResult.duplicateMap.get(hash)!;
              // Apply the CutoutPro URL to all duplicates
              await imageDeduplicationService.applyUrlToDuplicates(
                updatedAttachment.cutoutProImageUrl,
                updatedAttachment,
                duplicates
              );
            }
          } catch (error) {
            console.error(`Failed to process attachment ${attachment.id}:`, error);
          }
        }
      }
    }
  }

  async processLineItem(lineItem: LineItem, productSku: ProductSku) {
    const services: WorkflowServices = {
      lineItemRepo: this.lineItemRepo,
      queueRepo: this.queueRepo,
      attachmentRepo: this.attachmentRepo,
      cutoutProApiService: this.cutoutProApiService,
      imageDeduplicationService: this.imageDeduplicationService,
    };

    // Set initial status
    if (!lineItem.status) {
      lineItem.status = 'Line Item Received';
      await this.lineItemRepo.save(lineItem);
    }

    // Remove existing queue assignments
    // await this.queueRepo.delete({ lineItem: { id: lineItem.id } });
    // if (lineItem.cutoutProImages) {
    //   await this.queueRepo.delete({
    //     cutoutProImage: { id: In(lineItem.cutoutProImages.map(i => i.id)) },
    //   });
    // }
    // if (lineItem.attachments) {
    //   await this.queueRepo.delete({
    //     attachment: { id: In(lineItem.attachments.map(a => a.id)) },
    //   });
    // }

    // Apply workflows
    for (const workflow of this.workflows) {
      if (workflow.condition(productSku)) {
        await workflow.action(lineItem, productSku, services);
      }
    }
  }
}
