import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DeepPartial, ILike, Like, DataSource } from 'typeorm';
import { Order } from './entities/order.entity';
import { BaseService } from '../common/base.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { LineItem } from './entities/line-item.entity';
import { Attachment } from '../attachments/entities/attachment.entity';
import { StorageService } from '../attachments/services/storage.service';
import { FilterOrdersDto } from './dto/filter-orders.dto';
import { OrderStatus } from './enums/order.enums';
import { ProductSku } from '../product-sku/entities/product-sku.entity';
import { UpdateLineItemDto } from './dto/update-line-item.dto';
import { IOptions } from '../helpers/db.helpers';
import { RemakeLineItemDto } from './dto/remake-line-item.dto';
import { ProductKeyType } from 'src/constants/product-key-type';
import { User } from 'src/users/entities/user.entity';
import { accessEnv } from '../env.validation';
import { WorkflowService } from 'src/common/workflow.service';
interface ManualOrderLineItem {
  productSkuId: string;
  images?: string[];
}

@Injectable()
export class OrdersService extends BaseService<Order> {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(LineItem)
    private readonly lineItemRepository: Repository<LineItem>,
    @InjectRepository(Attachment)
    private readonly attachmentRepository: Repository<Attachment>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(ProductSku)
    private readonly productSkuRepository: Repository<ProductSku>,
    private readonly storageService: StorageService,
    private readonly workflowService: WorkflowService,
    private readonly dataSource: DataSource,
  ) {
    super(orderRepository);
  }

  async filterOrders(filterDto: FilterOrdersDto) {
    const { filters, page, limit } = filterDto;

    const processedFilters = await Promise.all(
      filters.map(async filterGroup => {
        return Promise.all(
          filterGroup.map(async filter => {
            if (filter.attribute === 'priorities') {
              // Handle priorities array field
              return {
                attribute: filter.attribute,
                operator: filter.operator,
                effectiveValue: Array.isArray(filter.value)
                  ? filter.value
                  : [filter.value],
              };
            }

            if (
              filter.attribute === 'statusUpdatedAt' &&
              typeof filter.value === 'number'
            ) {
              const days = filter.value;

              return {
                attribute: filter.attribute,
                operator: filter.operator,
                effectiveValue: days,
              };
            }

            return {
              attribute: filter.attribute,
              operator: filter.operator,
              effectiveValue: filter.value,
            };
          }),
        );
      }),
    );

    return this.findAllWithAdvancedFilters({
      filters: processedFilters,
      page,
      limit,
      relations: ['lineItems'],
    });
  }

  async createManualOrder(createOrder: any) {
    const { lineItems: _, ...ordersData } = createOrder;
    const lastOrder = await this.orderRepository.findOne({
      where: {
        shopifyOrderNumber: Like('EO-%'),
      },
      order: { shopifyOrderNumber: 'DESC' },
    });

    const orderNumber = lastOrder
      ? `EO-${Number(lastOrder.shopifyOrderNumber.split('-')[1]) + 1}`
      : 'EO-1000';
    const orderData = {
      ...ordersData,
      shopifyOrderNumber: orderNumber,
    };
    const order = await this.orderRepository.save(orderData);
    const rushKeys = {
      pajama_rush: 'Pajama Rush',
      socks_rush: 'Socks Rush',
      blanket_rush: 'Blanket Rush',
    };
    const rushResults = {};

    Object.keys(rushKeys).forEach(rushKey => {
      const rushValues = createOrder.lineItems
        .filter((item: any) =>
          item.properties?.some((prop: any) => prop.name === rushKey),
        )
        .map(
          (item: any) =>
            item.properties.find((prop: any) => prop.name === rushKey).value,
        );

      if (
        rushValues.length > 0 &&
        rushValues.every((val: any) => val === rushValues[0])
      ) {
        rushResults[rushKey] = rushKeys[rushKey];
      } else {
        rushResults[rushKey] = null;
      }
    });

    const lineItems = await Promise.all(
      createOrder.lineItems.map(async (item: any, index: number) => {
        let priority: string | null = null;
        const isAddon = item.properties?.some(
          (prop: any) => prop.name === 'is_addon' && prop.value === true,
        );
        const hasGiftBox =
          !isAddon &&
          item.properties?.some(
            (prop: any) => prop.name === 'pajama_gift_box',
          ) &&
          createOrder.lineItems.some((lineItem: any) => {
            const isLineItemAddon = lineItem.properties?.some(
              (p: any) => p.name === 'is_addon' && p.value === true,
            );
            const hasMatchingGiftBox = lineItem.properties?.some(
              (p: any) =>
                p.name === 'pajama_gift_box' &&
                p.value ===
                  item.properties.find(
                    (prop: any) => prop.name === 'pajama_gift_box',
                  )?.value,
            );
            return isLineItemAddon && hasMatchingGiftBox;
          });

        const originalKeyLineItem = item.properties?.find(
          prop => prop.name === '_original_unique_key',
        );

        const originalUniqueKey = originalKeyLineItem?.value;

        if (originalUniqueKey) {
          let digitalDownload = false;
          const matchingLineItems = createOrder.lineItems.filter(item =>
            item.properties?.find(
              prop =>
                prop.name === '_unique_key' && prop.value === originalUniqueKey,
            ),
          );
          const hasBackEngraving = matchingLineItems.some(item =>
            item.title?.toLowerCase().includes('back engraving'),
          );

          if (!hasBackEngraving && originalKeyLineItem) {
            const existingProperties = item.properties || [];
            // Find all back engraving properties
            const backEngravingProps = existingProperties.filter(prop =>
              prop.name.startsWith('Back Engravings '),
            );
            // For each existing back engraving
            backEngravingProps.forEach(prop => {
              const engravingNumber = prop.name.split(' ').pop(); // Get the number
              // Find and remove "Back Engraving X"
              const backEngravingPropIndex = existingProperties.findIndex(
                p => p.name === `Back Engravings ${engravingNumber}`,
              );
              if (backEngravingPropIndex >= 0) {
                existingProperties.splice(backEngravingPropIndex, 1);
              }
              // Find and remove "Back Engraving text X"
              const backEngravingTextPropIndex = existingProperties.findIndex(
                p => p.name === `Back Engraving text ${engravingNumber}`,
              );
              if (backEngravingTextPropIndex >= 0) {
                existingProperties.splice(backEngravingTextPropIndex, 1);
              }
            });
            item.properties = existingProperties;
          }
          for (const matchingLineItem of matchingLineItems) {
            if (matchingLineItem.name === ProductKeyType.DIGITAL_DOWNLOAD) {
              digitalDownload = true;
            } else if (
              matchingLineItem.title ===
              ProductKeyType.PORTRAIT_PRIORITY_CREATION
            ) {
              priority = 'Portrait Rush';
            } else if (
              matchingLineItem.title ===
              ProductKeyType.JEWELRY_PRIORITY_CREATION
            ) {
              priority = 'Jewelry Rush';
            } else if (
              matchingLineItem.title === ProductKeyType.RUSH_PRIORITY_CREATION
            ) {
              priority = matchingLineItem.variant_title;
            }
          }
        }

        if (!isAddon) {
          // Check for rush priority
          for (const rushKey of Object.keys(rushKeys)) {
            const rushProp = item.properties?.find(
              (prop: any) => prop.name === rushKey,
            );
            if (rushResults[rushKey] && rushProp) {
              // Verify there's a matching addon with same rush value
              const hasMatchingAddon = createOrder.lineItems.some(
                (lineItem: any) => {
                  const isItemAddon = lineItem.properties?.some(
                    (p: any) => p.name === 'is_addon' && p.value === true,
                  );
                  const hasMatchingRush = lineItem.properties?.some(
                    (p: any) =>
                      p.name === rushKey && p.value === rushProp.value,
                  );
                  return isItemAddon && hasMatchingRush;
                },
              );

              if (hasMatchingAddon) {
                priority = rushResults[rushKey];
                break;
              }
            }
          }
        }
        let sku;
        if (item.sku) {
          sku = await this.productSkuRepository.findOne({
            where: {
              sku: item.sku,
            },
          });
        } else {
          const addonProperty = item.properties?.find(
            (prop: any) => prop.name === 'is_addon',
          );
          if (addonProperty?.value === true) {
            const variantId = item.properties?.find(
              (prop: any) => prop.name !== 'is_addon',
            )?.value;
            if (variantId) {
              sku = await this.productSkuRepository.findOne({
                where: {
                  variantId: variantId.toString(),
                },
              });
            }
          }
        }

        return {
          shopifyItemId: item.id,
          itemNumber: `${order.shopifyOrderNumber}-${index + 1}`,
          quantity: item.quantity,
          metadata: item.properties?.reduce(
            (acc, prop) => ({
              ...acc,
              [prop.name]: prop.value,
            }),
            {},
          ),
          priority: priority || null,
          hasGift: hasGiftBox,
          productSku: sku,
          order,
        };
      }),
    );
    const savedLineItems = await this.lineItemRepository.save(lineItems);
    const priorities = lineItems.map(item => item.priority).filter(Boolean);
    const uniquePriorities = [...new Set(priorities)];
    order.priorities = uniquePriorities;
    await this.orderRepository.save(order);
  }

  async createOrderFromWebhook(body: any) {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const order = await this.findOrCreateOrderFromWebhook(body);
      await this.findOrCreateLineItemsFromWebhook(body, order);
      const lineItems = await this.lineItemRepository.find({
        where: { order: { id: order.id } },
      });
      const priorities = lineItems.map(item => item.priority).filter(Boolean);
      const uniquePriorities = [...new Set(priorities)] as string[];
      order.priorities = uniquePriorities;
      await this.orderRepository.save(order);
      await queryRunner.commitTransaction();
      return order;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async findOrCreateOrderFromWebhook(body: any) {
    const conditions = {
      shopifyOrderId: body.id,
    };

    let order = await this.orderRepository.findOneBy(conditions);

    if (!order) {
      const payment = {
        subtotal_price: body.current_subtotal_price,
        shipping_price: body.current_shipping_price_set.shop_money.amount,
        tax: body.current_total_tax,
        discount: body.current_total_discounts,
        total_price: body.current_total_price,
      };
      const orderDto = {
        ...conditions,
        shopifyOrderNumber: body.order_number,
        orderDate: body.created_at,
        orderStatus:
          body.fulfillment_status === 'partial'
            ? OrderStatus.PARTIALLY_FULFILLED
            : body.fulfillment_status === 'fulfilled'
              ? OrderStatus.FULFILLED
              : OrderStatus.UNFULFILLED,
        statusUpdatedAt: new Date(),
        customerFirstName: body.customer.first_name,
        customerLastName: body.customer.last_name,
        customerEmail: body.customer.email,
        customerPhoneNumber: body.customer.default_address.phone,
        shopifyCustomerId: body.customer.id,
        itemCount: body.line_items.length,
        shippingAddress: body.shipping_address,
        billingAddress: body.billing_address,
        paymentInformation: payment,
      };
      order = this.orderRepository.create(orderDto);
    }
    return await this.orderRepository.save(order);
  }

  private async findOrCreateLineItemsFromWebhook(body: any, order: Order) {
    const rushPriority = {
      pajama: 'Pajama Rush',
      socks: 'Socks Rush',
      blanket: 'Blanket Rush',
    };
    // const nonPlushLineItems = body.line_items.filter(item =>
    //   item.properties?.some(
    //     prop => prop.name === '_rush_key' || prop.name === '_original_rush_key',
    //   ),
    // );
    const lineItems = await Promise.all(
      body.line_items.map(async (lineItem, index) => {
        let computedPriority = (() => {
          // Check if there's any item with _rush_key for this product type
          const hasMatchingRushKey = body.line_items.some(item => {
            const rushKeyValue = item.properties?.find(
              prop => prop.name === '_rush_key',
            )?.value;
            const originalRushKeyValue = lineItem.properties?.find(
              prop => prop.name === '_original_rush_key',
            )?.value;
            return rushKeyValue === originalRushKeyValue;
          });

          // If there's a matching rush key in any item, get the priority for this item's original rush key
          if (hasMatchingRushKey) {
            const originalRushKeyValue = lineItem.properties?.find(
              prop => prop.name === '_original_rush_key',
            )?.value;
            return rushPriority[originalRushKeyValue];
          }
          return null;
        })();
        const originalKeyLineItem = lineItem.properties?.find(
          prop => prop.name === '_original_unique_key',
        );

        const originalUniqueKey = originalKeyLineItem?.value;

        let digitalDownload = false;
        if (originalUniqueKey) {
          const matchingLineItems = body.line_items.filter(item =>
            item.properties?.find(
              prop =>
                prop.name === '_unique_key' && prop.value === originalUniqueKey,
            ),
          );
          const hasBackEngraving = matchingLineItems.some(item =>
            item.title?.toLowerCase().includes('back engraving'),
          );

          if (!hasBackEngraving && originalKeyLineItem) {
            const existingProperties = lineItem.properties || [];
            // Find all back engraving properties
            const backEngravingProps = existingProperties.filter(prop =>
              prop.name.startsWith('Back Engravings '),
            );
            // For each existing back engraving
            backEngravingProps.forEach(prop => {
              const engravingNumber = prop.name.split(' ').pop(); // Get the number
              // Find and remove "Back Engraving X"
              const backEngravingPropIndex = existingProperties.findIndex(
                p => p.name === `Back Engravings ${engravingNumber}`,
              );
              if (backEngravingPropIndex >= 0) {
                existingProperties.splice(backEngravingPropIndex, 1);
              }
              // Find and remove "Back Engraving text X"
              const backEngravingTextPropIndex = existingProperties.findIndex(
                p => p.name === `Back Engraving text ${engravingNumber}`,
              );
              if (backEngravingTextPropIndex >= 0) {
                existingProperties.splice(backEngravingTextPropIndex, 1);
              }
            });

            lineItem.properties = existingProperties;
          }
          for (const matchingLineItem of matchingLineItems) {
            if (matchingLineItem.name === ProductKeyType.DIGITAL_DOWNLOAD) {
              digitalDownload = true;
            } else if (
              matchingLineItem.title ===
              ProductKeyType.PORTRAIT_PRIORITY_CREATION
            ) {
              computedPriority = 'Portrait Rush';
            } else if (
              matchingLineItem.title ===
              ProductKeyType.JEWELRY_PRIORITY_CREATION
            ) {
              computedPriority = 'Jewelry Rush';
            } else if (
              matchingLineItem.title === ProductKeyType.RUSH_PRIORITY_CREATION
            ) {
              computedPriority = matchingLineItem.variant_title;
            }
          }
        }

        const computedSku = (() => {
          let computedSku = '';
          if (JSON.parse(accessEnv('SKU_TRANSFORMER'))) {
            computedSku = lineItem.sku;
          } else {
            const size = lineItem.properties?.find(
              prop => prop.name === 'Size',
            )?.value;
            const background = lineItem.properties?.find(
              prop => prop.name === 'Background',
            )?.value;
            computedSku = lineItem.sku;
            if (size) {
              computedSku += ` - ${size}`;
            }
            if (background) {
              computedSku += ` - ${background}`;
            }
          }
          return computedSku;
        })();

        const productSku = !computedSku
          ? null
          : await this.productSkuRepository.findOne({
              where: { sku: computedSku },
              relations: ['products', 'artworkType'],
            });

        const product = productSku?.products?.[0];

        const itemNumber = `CC-${order.shopifyOrderNumber}-${index + 1}`;
        let lineItemEntity = await this.lineItemRepository.findOne({
          where: { shopifyItemId: lineItem.id },
        });

        if (lineItemEntity) {
          lineItemEntity.shopifyItemId = lineItem.id;
          lineItemEntity.quantity = lineItem.quantity;
          lineItemEntity.metadata = lineItem.properties?.reduce(
            (acc, prop) => ({
              ...acc,
              [prop.name]: prop.value,
            }),
            {},
          );
          lineItemEntity.priority = computedPriority;
          if (productSku) {
            lineItemEntity.productSku = productSku;
          }
          if (product) {
            lineItemEntity.product = product;
          }
          if (digitalDownload) {
            lineItemEntity.digitalDownload = digitalDownload;
          }
          lineItemEntity.order = order;

          await this.lineItemRepository.save(lineItemEntity);
          return lineItemEntity;
        } else {
          lineItemEntity = this.lineItemRepository.create({
            shopifyItemId: lineItem.id,
            itemNumber,
            quantity: lineItem.quantity,
            metadata: lineItem.properties?.reduce(
              (acc, prop) => ({
                ...acc,
                [prop.name]: prop.value,
              }),
              {},
            ),
            priority: computedPriority,
            digitalDownload: digitalDownload,
            productSku: productSku || null,
            product,
            order,
          } as DeepPartial<LineItem>);
        }
        const savedLineItem =
          await this.lineItemRepository.save(lineItemEntity);
        await this.attachImagesToLineItems([savedLineItem]);
        return { savedLineItem, productSku };
      }),
    );

    console.log("lineItems------", lineItems);

    // Debug: Check why productSku is null
    for (const item of lineItems) {
      console.log(`Line item ${item.savedLineItem.itemNumber}: productSku = ${item.productSku}`);
    }

    // Collect all line items with their product SKUs for cross-line-item processing
    const lineItemsWithSkus: Array<{ lineItem: LineItem; productSku: ProductSku | null }> = [];
    for (const item of lineItems) {
      if (item.savedLineItem) {
        const lineItemWithAttachment = await this.lineItemRepository.findOne({
          where: { id: item.savedLineItem.id },
          relations: ['attachments', 'order'],
        });

        if (lineItemWithAttachment) {
          lineItemsWithSkus.push({
            lineItem: lineItemWithAttachment,
            productSku: item.productSku
          });
          console.log(`Added line item ${lineItemWithAttachment.itemNumber} with ${lineItemWithAttachment.attachments?.length || 0} attachments`);
        }
      }
    }

    console.log(`Total line items for processing: ${lineItemsWithSkus.length}`);

    // Process all line items together with cross-line-item deduplication
    if (lineItemsWithSkus.length > 0) {
      await this.workflowService.processMultipleLineItems(lineItemsWithSkus);
    }

    return lineItems.map(item => item.savedLineItem);
  }

  private async attachImagesToLineItems(lineItems: LineItem[]) {
    const attachmentsToCreate = lineItems.flatMap(lineItem => {
      const metadata = lineItem.metadata ?? {};
      const attachments = Object.entries(metadata)
        .filter(([key, _]) => key.includes('image_url'))
        .map(([key, value]) => ({
          filename: key,
          url: value,
          size: 0,
          mimetype: 'image/jpeg',
          lineItem,
        }));
      return attachments;
    });
    if (attachmentsToCreate.length > 0) {
      return await this.attachmentRepository.save(attachmentsToCreate);
    }
    return [];
  }

  async findLineItemById(id: string) {
    const lineItem = await this.lineItemRepository.findOne({
      where: { id },
      relations: ['productSku', 'product', 'requests', 'order', 'attachments'],
    });

    if (!lineItem) {
      throw new HttpException('Line item not found', HttpStatus.NOT_FOUND);
    }

    return lineItem;
  }

  async updateLineItem(
    id: string,
    updateLineItemDto: UpdateLineItemDto,
    user: any,
  ) {
    const lineItem = await this.findLineItemById(id);

    const { flagged, flagReason, productSkuId, status, cancelReason, ...rest } =
      updateLineItemDto;

    if (flagged !== undefined && lineItem.flagged !== flagged) {
      const order = await this.orderRepository.findOne({
        where: { id: lineItem.order.id },
        relations: ['lineItems'],
      });

      const flaggedLineItems = order?.lineItems.filter(
        item => item.flagged === true,
      ).length;

      lineItem.flagReason = flagReason || '';
      lineItem.flagged = flagged;

      if (order && flaggedLineItems === 1 && !flagged) {
        order.flagged = false;
        order.flaggedAt = new Date();
        await this.orderRepository.save(order);
      } else if (order && flagged) {
        order.flagged = true;
        order.flaggedAt = new Date();
        await this.orderRepository.save(order);
      }
    }

    if (productSkuId) {
      const productSku = await this.productSkuRepository.findOne({
        where: { id: productSkuId },
      });
      if (!productSku) {
        throw new HttpException('Product SKU not found', HttpStatus.NOT_FOUND);
      }
      lineItem.productSku = productSku;
    }

    // Handle cancellation status
    if (status?.toLowerCase() === 'cancelled') {
      if (!cancelReason) {
        throw new HttpException(
          'Cancel reason is required when cancelling an item',
          HttpStatus.BAD_REQUEST,
        );
      }
      const foundUser = await this.userRepository.findOne({
        where: { email: user?.email },
      });
      if (!foundUser) {
        throw new HttpException(
          'User not found for cancellation',
          HttpStatus.BAD_REQUEST,
        );
      }
      lineItem.cancelReason = {
        status: status,
        reason: cancelReason,
        timestamp: new Date(),
        username: `${foundUser.firstName} ${foundUser.lastName}`,
      };
      lineItem.status = status;
    }
    Object.assign(lineItem, rest);

    await this.lineItemRepository.save(lineItem);
    return this.findLineItemById(id);
  }

  async updateById(id: string, updateDto: DeepPartial<Order>): Promise<Order> {
    const order = await this.findByIdOrThrow(id);

    if (updateDto.orderStatus && order.orderStatus !== updateDto.orderStatus) {
      updateDto.statusUpdatedAt = new Date();
    }

    Object.assign(order, updateDto);
    return await this.orderRepository.save(order);
  }

  async findAll(sOptions?: IOptions<Order>) {
    const { q, sort } = sOptions || {};

    if (q && q.includes('flagged')) {
      const flagcheck = q.split(':')[2].toLowerCase();
      const sortField =
        flagcheck === 'true' ? 'flaggedAt:asc' : 'orderDate:desc';
      return super.findAll({
        ...sOptions,
        sort: sort || sortField,
      });
    }

    if (!sort) {
      return super.findAll({
        ...sOptions,
        sort: 'orderDate:desc',
      });
    }

    return super.findAll(sOptions);
  }

  async remakeLineItem(remakeDto: RemakeLineItemDto, user: any) {
    const lineItem = await this.findLineItemById(remakeDto.lineItemId);

    // Extract the base item number without the '-R' suffix
    const baseItemNumber = lineItem.itemNumber.replace(/-R\d+$/, '');

    const existingRemakes = await this.lineItemRepository.count({
      where: {
        itemNumber: ILike(`${baseItemNumber}%`),
        isRemake: true,
      },
    });

    const nextRemakeNumber = existingRemakes + 1;

    const remakeSuffix = `-R${nextRemakeNumber}`;
    const newItemNumber = baseItemNumber + remakeSuffix;

    // Create a new line item based on the original
    const newLineItemData: DeepPartial<LineItem> = {
      shopifyItemId: lineItem.shopifyItemId,
      status: lineItem.status,
      itemNumber: newItemNumber,
      flagged: false,
      isRemake: false,
      flagReason: undefined,
      cancelReason: undefined,
      quantity: lineItem.quantity,
      priority: lineItem.priority,
      notes: lineItem.notes,
      metadata: remakeDto.metadata || lineItem.metadata,
      product: lineItem.product,
      order: lineItem.order,
      remakeReason: remakeDto.remakeReason,
      detailedRemakeReason: remakeDto.detailedRemakeReason,
    };

    // If a new productSkuId is provided, fetch and set it
    if (remakeDto.productSkuId) {
      const productSku = await this.productSkuRepository.findOne({
        where: { id: remakeDto.productSkuId },
      });
      if (!productSku) {
        throw new HttpException('Product SKU not found', HttpStatus.NOT_FOUND);
      }
      newLineItemData.productSku = productSku;
    } else {
      newLineItemData.productSku = lineItem.productSku;
    }

    const order = await this.orderRepository.findOne({
      where: { id: lineItem.order.id },
    });

    if (!order) {
      throw new HttpException('Order not found', HttpStatus.NOT_FOUND);
    }
    order.itemCount += 1;
    await this.orderRepository.save(order);

    const savedLineItem = await this.lineItemRepository.save(newLineItemData);

    lineItem.isRemake = true;
    await this.lineItemRepository.save(lineItem);

    return this.findLineItemById(savedLineItem.id);
  }
}
