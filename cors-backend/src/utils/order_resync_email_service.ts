import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { RECIPIENT_EMAILS } from '../constants/emails.constant';
@Injectable()
export class OrderResyncEmailService {
  private transporter: nodemailer.Transporter;
  private readonly logger = new Logger(OrderResyncEmailService.name);

  constructor(private configService: ConfigService) {
    const host = this.configService.get('SMTP_HOST');
    const port = this.configService.get('SMTP_PORT');
    const user = this.configService.get('SMTP_USERNAME');
    const pass = this.configService.get('SMTP_PASSWORD');

    if (!host || !port || !user || !pass) {
      this.logger.error('Missing SMTP configuration');
      throw new Error('Missing SMTP configuration');
    }

    this.logger.log(`Initializing SMTP transport with host: ${host}, port: ${port}`);

    this.transporter = nodemailer.createTransport({
      host,
      port,
      auth: {
        user,
        pass,
      },
    });
  }

  async sendOrderResyncEmail(
    syncedOrders: number[],
    notSyncedOrders: number[],
    shopName: string,
  ): Promise<void> {
    this.logger.log(`Attempting to send email for shop: ${shopName}`);
    this.logger.log(`Synced orders: ${syncedOrders.length}, Not synced orders: ${notSyncedOrders.length}`);

    const fromEmail = this.configService.get('SMTP_FROM_EMAIL');
    if (!fromEmail) {
      throw new Error('SMTP_FROM_EMAIL not configured');
    }

    const mailOptions = {
      from: `"Cuddle Clones - CORS" <${fromEmail}>`,
      to: RECIPIENT_EMAILS.ORDER_RESYNC,
      subject: 'CORS Missing Orders',
      html: `
        <p>
          Hi Customer Care,
        </p>
        ${syncedOrders.length > 0 ? `
          <p>
            The following orders have been resync to OMS from store ${shopName}:
          </p>
          <ul>
            ${syncedOrders.map(order => `<li>${order}</li>`).join('')}
          </ul>
        ` : ''}
        ${notSyncedOrders.length > 0 ? `
          <p>
            The following orders have not been resync to CORS by cron:
          </p>
          <ul>
            ${notSyncedOrders.map(order => `<li>${order}</li>`).join('')}
          </ul>
        ` : ''}
        <p>
          Thank You,<br>
          The Cuddle Clones Team
        </p>
      `,
    };

    try {
      this.logger.log('Sending email...');
      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully: ${JSON.stringify(info)}`);
    } catch (error) {
      this.logger.error('Error sending email:', error);
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }
}
