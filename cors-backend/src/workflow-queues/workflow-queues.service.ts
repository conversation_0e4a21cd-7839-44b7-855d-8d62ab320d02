import {
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Queue } from './entities/queue.entity';
import { BaseService } from 'src/common/base.service';
import { User } from 'src/users/entities/user.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { checkStatusTransition, userFullName } from 'src/utils/workflow.utils';
@Injectable()
export class QueuesService extends BaseService<Queue> {
  constructor(
    @InjectRepository(Queue)
    private queueRepository: Repository<Queue>,
    @InjectRepository(Attachment)
    private attachmentRepository: Repository<Attachment>,
    @InjectRepository(LineItem)
    private lineItemRepository: Repository<LineItem>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {
    super(queueRepository);
  }

  async findQueue(id: string, page: number, limit: number) {
    const queue = await this.queueRepository.findOne({
      where: {
        id: id,
      },
      relations: [
        'lineItems',
        'lineItems.attachments',
        'lineItems.productSku',
        'lineItems.order',
        'lineItems.assignedTo',
        'attachments',
        'attachments.lineItem',
        'attachments.lineItem.productSku',
        'attachments.lineItem.order',
        'attachments.assignedTo',
      ],
    });

    if (!queue) {
      throw new NotFoundException('Queue not found');
    }

    // Process attachments with pagination
    const sortedAttachments = queue.attachments
      .map(attachment => ({
        id: attachment.id,
        priority: attachment.lineItem?.priority,
        filename: attachment.filename,
        attachment_url: attachment.url,
        cutout_pro_url: attachment.cutoutProImageUrl,
        cropType: attachment.lineItem?.productSku?.cropType,
        order_number: attachment.lineItem?.order?.shopifyOrderNumber,
        order_date: attachment.lineItem?.order?.orderDate,
        assigned_to: attachment.assignedTo
          ? userFullName(attachment?.assignedTo)
          : null,
      }))
      .sort((a, b) => {
        if (a.priority && !b.priority) return -1;
        if (!a.priority && b.priority) return 1;
        if (a.priority && b.priority) {
          const priorityA = Number(a.priority);
          const priorityB = Number(b.priority);
          return priorityB - priorityA;
        }
        return 0;
      });

    // Process line items with pagination
    const sortedLineItems = queue.lineItems
      .map(lineItem => ({
        id: lineItem.id,
        priority: lineItem.priority,
        sku: lineItem.productSku.sku,
        attachments: lineItem.attachments.map(attachment => ({
          id: attachment.id,
          name: attachment.filename,
          status: attachment.status,
          attachment_url: attachment.url,
          cutout_pro_url: attachment.cutoutProImageUrl,
        })),
        order_number: lineItem.order.shopifyOrderNumber,
        order_date: lineItem.order.orderDate,
        line_item_status: lineItem.status,
        assigned_to: lineItem.assignedTo
          ? userFullName(lineItem.assignedTo)
          : null,
      }))
      .sort((a, b) => {
        if (a.priority && !b.priority) return -1;
        if (!a.priority && b.priority) return 1;
        if (a.priority && b.priority) {
          const priorityA = Number(a.priority);
          const priorityB = Number(b.priority);
          return priorityB - priorityA;
        }
        return 0;
      });

    // Apply pagination
    const paginatedAttachments = sortedAttachments.slice(
      (page - 1) * limit,
      page * limit,
    );
    const paginatedLineItems = sortedLineItems.slice(
      (page - 1) * limit,
      page * limit,
    );

    return {
      id: queue.id,
      name: queue.name,
      attachments: paginatedAttachments,
      lineItems: paginatedLineItems,
      page: page,
      limit: limit,
    };
  }

  async assignQueueItems(currentUser: User, queueId: string) {
    // Use a transaction to ensure atomicity and prevent race conditions
    return await this.queueRepository.manager.transaction(
      async transactionalEntityManager => {
        // First fetch the queue without locking to check if it exists
        const queue = await transactionalEntityManager.findOne(
          this.queueRepository.target,
          {
            where: { id: queueId },
          },
        );

        if (!queue) {
          throw new NotFoundException('Queue not found');
        }
        // Fetch attachments separately
        const attachments = await transactionalEntityManager
          .createQueryBuilder(Attachment, 'attachment')
          .leftJoinAndSelect('attachment.lineItem', 'lineItem')
          .leftJoinAndSelect('lineItem.order', 'order')
          .where('attachment.queueId = :queueId', { queueId })
          .andWhere('attachment.assignedTo IS NULL')
          .getMany();

        // Fetch line items separately
        const lineItems = await transactionalEntityManager
          .createQueryBuilder(LineItem, 'lineItem')
          .where('lineItem.queueId = :queueId', { queueId })
          .andWhere('lineItem.assignedTo IS NULL')
          .getMany();

        const userId = (currentUser as any).user;
        const user = await transactionalEntityManager.findOne(
          this.userRepository.target,
          {
            where: { id: userId },
            relations: ['lineItems', 'attachments', 'attachments.queue'],
          },
        );

        if (!user) {
          throw new NotFoundException('User not found');
        }

        // Sort attachments based on their line item's priority
        const sortedAttachments = attachments.sort((a, b) => {
          const priorityA = a.lineItem?.priority
            ? Number(a.lineItem.priority)
            : 0;
          const priorityB = b.lineItem?.priority
            ? Number(b.lineItem.priority)
            : 0;

          if (priorityA && !priorityB) return -1;
          if (!priorityA && priorityB) return 1;
          if (priorityA && priorityB) {
            return priorityB - priorityA; // Higher priority first
          }
          return 0;
        });

        // Sort line items based on priority
        const sortedLineItems = lineItems.sort((a, b) => {
          const priorityA = a.priority ? Number(a.priority) : 0;
          const priorityB = b.priority ? Number(b.priority) : 0;

          if (priorityA && !priorityB) return -1;
          if (!priorityA && priorityB) return 1;
          if (priorityA && priorityB) {
            return priorityB - priorityA; // Higher priority first
          }
          return 0;
        });

        const assignedAttachments: Attachment[] = [];
        const assignedLineItems: LineItem[] = [];
        if (user.attachments.length === 0 && sortedAttachments.length > 0) {
          const attachmentsToAssign = sortedAttachments.slice(
            0,
            queue.maxItemsToAssign,
          );
          for (const attachment of attachmentsToAssign) {
            attachment.assignedTo = user;
            await transactionalEntityManager.save(attachment);
            assignedAttachments.push(attachment);
          }
        }

        if (user.lineItems.length === 0 && sortedLineItems.length > 0) {
          const lineItemsToAssign = sortedLineItems.slice(
            0,
            queue.maxItemsToAssign,
          );
          for (const lineItem of lineItemsToAssign) {
            lineItem.assignedTo = user;
            await transactionalEntityManager.save(lineItem);
            assignedLineItems.push(lineItem);
          }
        }

        const userWithAssigned = await transactionalEntityManager.findOne(
          this.userRepository.target,
          {
            where: { id: userId },
            relations: ['lineItems', 'attachments'],
          },
        );
        if (!userWithAssigned) {
          throw new NotFoundException('User not found');
        }

        const modifiedAttachments = assignedAttachments.map(attachment => ({
          id: attachment.id,
          priority: attachment.lineItem?.priority,
          filename: attachment.filename,
          attachment_url: attachment.url,
          cutout_pro_url: attachment.cutoutProImageUrl,
          cropType: attachment.lineItem?.productSku?.cropType,
          order_number: attachment.lineItem?.order?.shopifyOrderNumber,
          order_date: attachment.lineItem?.order?.orderDate,
          assigned_to: userFullName(attachment.assignedTo),
          attachment_status: attachment.status,
          line_item_status: attachment.lineItem?.status,
        }));

        const total_assigned = userWithAssigned.attachments
          ? userWithAssigned.attachments.length
          : userWithAssigned.lineItems.length;
        return {
          total_assigned,
          attachments: modifiedAttachments[0],
          lineItems: assignedLineItems[0],
        };
      },
    );
  }

  async updateQueueSettings(
    queueSettings: Array<{ id: string; maxItemsToAssign: number }>,
  ) {
    const queues = await this.queueRepository.find({
      where: { id: In(queueSettings.map(q => q.id)) },
    });

    for (const queue of queues) {
      queue.maxItemsToAssign =
        queueSettings.find(q => q.id === queue.id)?.maxItemsToAssign ?? 5;
      await this.queueRepository.save(queue);
    }
    return queues;
  }

  async stopReview(currentUser: User, queueId: string) {
    const queue = await this.queueRepository.findOne({
      where: { id: queueId },
      relations: [
        'attachments',
        'lineItems',
        'attachments.assignedTo',
        'lineItems.assignedTo',
      ],
    });
    if (!queue) {
      throw new NotFoundException('Queue not found');
    }

    const userId = (currentUser as any).user;
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const hello = queue.attachments.filter(
      attachment => attachment.assignedTo?.id === user.id,
    );

    if (queue.attachments.length > 0) {
      for (const attachment of queue.attachments) {
        if (attachment.assignedTo?.id === user.id) {
          (attachment as any).assignedTo = null;
          await this.attachmentRepository.save(attachment);
        }
      }
    }

    if (queue.lineItems.length > 0) {
      for (const lineItem of queue.lineItems) {
        if (lineItem.assignedTo?.id === user.id) {
          (lineItem as any).assignedTo = null;
          await this.lineItemRepository.save(lineItem);
        }
      }
    }

    return {
      message: 'Review process stopped',
    };
  }

  async getQueueItem(currentUser: User, queueId: string) {
    const queue = await this.queueRepository.findOne({
      where: { id: queueId },
      relations: [
        'attachments',
        'lineItems',
        'attachments.assignedTo',
        'lineItems.assignedTo',
        'attachments.lineItem',
        'lineItems.order',
        'attachments.lineItem.productSku',
        'attachments.lineItem.order',
        'lineItems.productSku',
        'lineItems.product',
        'lineItems.attachments',
      ],
    });

    if (!queue) {
      throw new NotFoundException('Queue not found');
    }

    const userId = (currentUser as any).user;
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }
    const attachments = queue.attachments
      .filter(attachment => attachment.assignedTo?.id === user.id)
      .map(attachment => ({
        id: attachment.id,
        priority: attachment.lineItem?.priority,
        filename: attachment.filename,
        attachment_url: attachment.url,
        cutout_pro_url: attachment.cutoutProImageUrl,
        cropType: attachment.lineItem?.productSku?.cropType,
        order_number: attachment.lineItem?.order?.shopifyOrderNumber,
        order_date: attachment.lineItem?.order?.orderDate,
        assigned_to: attachment.assignedTo
          ? userFullName(attachment.assignedTo)
          : null,
        attachment_status: attachment.status,
        line_item_status: attachment.lineItem?.status,
      }));
    const lineItems = queue.lineItems
      .filter(lineItem => lineItem.assignedTo?.id === user.id)
      .map(lineItem => ({
        id: lineItem.id,
        priority: lineItem.priority,
        sku: lineItem.productSku.sku,
        product_name: lineItem.product?.name,
        attachments: lineItem.attachments.map(attachment => ({
          id: attachment.id,
          name: attachment.filename,
          status: attachment.status,
          attachment_url: attachment.url,
          cutout_pro_url: attachment.cutoutProImageUrl,
        })),
        order_number: lineItem.order.shopifyOrderNumber,
        order_date: lineItem.order.orderDate,
        line_item_status: lineItem.status,
        assigned_to: lineItem.assignedTo
          ? userFullName(lineItem.assignedTo)
          : null,
      }));

    if (attachments.length <= 0 && lineItems.length <= 0) {
      const queueItem = await this.assignQueueItems(currentUser, queueId);
      return queueItem;
    } else {
      return {
        attachments: attachments[0],
        lineItems: lineItems[0],
      };
    }
  }

  async updateQueueItemStatus(
    currentUser: User,
    queuePayload: {
      queueId: string;
      itemId: string;
      action: string;
      type: string;
    },
  ) {
    const userId = (currentUser as any).user;
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const { queueId, itemId, action, type } = queuePayload;

    if (type === 'attachment') {
      const attachment = await this.attachmentRepository.findOne({
        where: { id: itemId },
        relations: ['lineItem', 'assignedTo'],
      });
      if (!attachment) {
        throw new NotFoundException('Attachment not found');
      }

      if (attachment.assignedTo?.id !== user.id) {
        throw new ForbiddenException(
          'You are not authorized to update this attachment',
        );
      }

      if (action === 'approve') {
        attachment.status = 'Crop Approved';
        (attachment as any).queue = null;
        (attachment as any).assignedTo = null;
        await this.attachmentRepository.save(attachment);

        const lineItem = await this.lineItemRepository.findOne({
          where: { id: attachment.lineItem.id },
          relations: ['attachments'],
        });

        if (lineItem) {
          const allAttachmentsApproved = lineItem.attachments.every(
            att => att.status === 'Crop Approved',
          );
          if (allAttachmentsApproved) {
            checkStatusTransition(lineItem.status, 'Ready for Vendor');
            lineItem.status = 'Ready for Vendor';
            await this.lineItemRepository.save(lineItem);
          }
        }
      }

      if (action === 'deny') {
        attachment.status = 'Crop Denied';
        let cropNeededQueue = await this.queueRepository.findOne({
          where: { name: 'Crop Needed' },
        });
        if (!cropNeededQueue) {
          cropNeededQueue = await this.queueRepository.create({
            name: 'Crop Needed',
          });
          await this.queueRepository.save(cropNeededQueue);
        }
        attachment.queue = cropNeededQueue;
        (attachment as any).assignedTo = null;
        await this.attachmentRepository.save(attachment);
        const currentStatus = attachment.lineItem.status;
        if (
          currentStatus !== 'Crop Needed' &&
          currentStatus !== 'Awaiting Customer Response'
        ) {
          checkStatusTransition(currentStatus, 'Crop Needed');
          attachment.lineItem.status = 'Crop Needed';
          await this.lineItemRepository.save(attachment.lineItem);
        }
      }

      if (action === 'new-image-requested') {
        attachment.status = 'New Image Requested';
        (attachment as any).queue = null;
        (attachment as any).assignedTo = null;
        await this.attachmentRepository.save(attachment);
        if (attachment.lineItem.status !== 'Awaiting Customer Response') {
          checkStatusTransition(
            attachment.lineItem.status,
            'Awaiting Customer Response',
          );
          attachment.lineItem.status = 'Awaiting Customer Response';
          await this.lineItemRepository.save(attachment.lineItem);
        }
      }
    }
    const queueItem = await this.getQueueItem(currentUser, queueId);
    return queueItem;
  }

  async uploadCompletedArtFile(
    currentUser: User,
    attachmentId: string,
    completedArtFileUrl: string,
    queueId: string,
  ) {
    const attachment = await this.attachmentRepository.findOne({
      where: { id: attachmentId },
      relations: ['lineItem', 'queue', 'assignedTo'],
    });
    if (!attachment) {
      throw new NotFoundException('Attachment not found');
    }
    const userId = (currentUser as any).user;
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (attachment.assignedTo?.id !== user.id) {
      throw new ForbiddenException(
        'You are not authorized to update this attachment',
      );
    }
    try {
      await this.attachmentRepository.manager.transaction(
        async transactionalEntityManager => {
          attachment.completedArtFileUrl = completedArtFileUrl;
          attachment.status = 'Crop Approved';
          (attachment as any).queue = null;
          (attachment as any).assignedTo = null;
          await transactionalEntityManager.save(attachment);

          const lineItem = await transactionalEntityManager.findOne(LineItem, {
            where: { id: attachment.lineItem.id },
            relations: ['attachments'],
          });

          if (lineItem) {
            const allAttachmentsApproved = lineItem.attachments.every(
              att => att.status === 'Crop Approved',
            );
            if (allAttachmentsApproved) {
              checkStatusTransition(lineItem.status, 'Ready for Vendor');
              lineItem.status = 'Ready for Vendor';
              await transactionalEntityManager.save(lineItem);
            }
          }
        },
      );
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to update attachment and line item: ${error.message}`,
      );
    }
    const queueItem = await this.getQueueItem(currentUser, queueId);
    return queueItem;
  }
}
