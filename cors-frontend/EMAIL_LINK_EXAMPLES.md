# Email Link Examples for Order Status

## URL Formats for Different Form Types

When sending emails to customers, use these URL formats based on the action needed:

### 1. General Order Status (Default)
```
https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber=124453&customerEmail=<EMAIL>
```

### 2. New Image Request Form
```
https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber=124453&customerEmail=<EMAIL>&newImageRequest
```

### 3. Customer Contact Needed Form
```
https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber=124453&customerEmail=<EMAIL>&customerContactNeeded
```

### 4. Customer Approval Form
```
https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber=124453&customerEmail=<EMAIL>&customerApproval
```

### 5. Direct Order Number in Path (any form type)
```
https://cors-dev.cuddleclones.com/order-status/124453?newImageRequest
https://cors-dev.cuddleclones.com/order-status/124453?customerContactNeeded
https://cors-dev.cuddleclones.com/order-status/124453?customerApproval
```

## Form-Specific Features

### New Image Request Form
**When to use:** When an image is rejected and customer needs to upload a new one
**Features:**
- Shows the rejected image with rejection reason
- Allows customer to upload a new image
- Checkbox for "I don't have this" option
- Free-form text field for additional information
- Image level processing

### Customer Contact Needed Form
**When to use:** When artwork team needs customer input on a specific image
**Features:**
- Shows the selected reference image
- Displays message from the artwork team
- Allows customer to upload a new image (optional)
- Free-form text field for customer response
- Item level processing

### Customer Approval Form
**When to use:** When artwork is ready for customer approval
**Features:**
- Shows the artwork/image for approval
- "Approve" button → Changes status to "Ready for Vendor"
- "Request Revision" button → Opens revision dialog
- Revision dialog includes:
  - Free-form text field: "What Revision Are You Requesting?"
  - Submit button → Changes status to "Revision Requested"
  - Cancel button → Closes dialog
- Logs requests with timestamps and submitter info
- Adds text to item notes in Order Detail page

## How It Works

### Server-Side Rendering (SSR)
1. Customer clicks the email link
2. Next.js server receives the request
3. Server extracts `shopifyOrderNumber`, `customerEmail`, and form type from URL parameters
4. Server calls the backend API: `GET /orders/public/status/124453`
5. Server renders the page with the order data and appropriate form already loaded
6. Customer sees the order status and form immediately (no loading spinner)

### URL Parameter Extraction
The page component automatically extracts:
- `shopifyOrderNumber` from `?shopifyOrderNumber=124453`
- `customerEmail` from `&customerEmail=<EMAIL>`
- Form type from `&newImageRequest`, `&customerContactNeeded`, or `&customerApproval`

### Error Handling
If the order is not found or there's an error:
- The error is handled server-side
- Customer sees an appropriate error message
- No additional client-side API calls needed

## Email Template Example

```html
<p>Dear {{customerFirstName}},</p>
<p>Your order #{{shopifyOrderNumber}} status has been updated.</p>
<p><a href="https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber={{shopifyOrderNumber}}&customerEmail={{customerEmail}}">
   Click here to view your order status
</a></p>
```

## Benefits

1. **Fast Loading**: Order data is fetched server-side
2. **SEO Friendly**: Content is rendered on the server
3. **No Authentication Required**: Public access via URL parameters
4. **Better UX**: No loading spinners for customers
5. **Email Tracking**: Can track which customers clicked the links

## Security Notes

- Only public order information is exposed
- No sensitive customer data in the API response
- Order lookup is limited to order number only
- Customer email can be used for additional validation if needed
