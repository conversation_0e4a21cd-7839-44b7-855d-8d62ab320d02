# Testing the Public Order Status Feature

## Backend API Testing

### 1. Test the Public Endpoint
```bash
# Test with a valid order number
curl -X GET "http://localhost:3000/orders/public/status/1234567890"

# Expected response:
{
  "orderNumber": "1234567890",
  "orderDate": "2024-01-15T10:30:00Z",
  "orderStatus": "unfulfilled",
  "customerFirstName": "John",
  "customerLastName": "Doe",
  "itemCount": 2,
  "lineItems": [
    {
      "id": "line-item-1",
      "itemNumber": "ITEM-001",
      "productSku": {
        "sku": "SKU-123"
      },
      "quantity": 1,
      "status": "pending"
    }
  ]
}
```

### 2. Test Error Cases
```bash
# Test with invalid order number
curl -X GET "http://localhost:3000/orders/public/status/invalid-order"

# Expected response (404):
{
  "statusCode": 404,
  "message": "Order not found"
}
```

## Frontend Testing

### 1. Test Direct URL Access
Open these URLs in your browser:

```
# With query parameters (recommended for email links)
http://localhost:3001/order-status?shopifyOrderNumber=1234567890&customerEmail=<EMAIL>

# With path parameter
http://localhost:3001/order-status/1234567890

# General page (no auto-load)
http://localhost:3001/order-status
```

### 2. Test Server-Side Rendering
1. Open browser developer tools
2. Go to Network tab
3. Visit the order status URL
4. Check that the page loads with data immediately (no loading spinner)
5. Verify that the order data is rendered server-side

### 3. Test Error Handling
```
# Test with invalid order number
http://localhost:3001/order-status?shopifyOrderNumber=invalid-order
```

## Email Link Testing

### 1. Create Test Email Template
```html
<!DOCTYPE html>
<html>
<body>
  <h2>Order Status Update</h2>
  <p>Dear Customer,</p>
  <p>Your order #1234567890 has been updated.</p>
  <p>
    <a href="http://localhost:3001/order-status?shopifyOrderNumber=1234567890&customerEmail=<EMAIL>">
      View Order Status
    </a>
  </p>
</body>
</html>
```

### 2. Test Email Link Flow
1. Send test email with the link
2. Click the link from email client
3. Verify it opens in new browser tab
4. Verify order data loads automatically
5. Test "Request New Image" functionality

## Production Testing

### 1. Update URLs for Production
Replace `localhost:3001` with your production domain:
```
https://cors-dev.cuddleclones.com/order-status?shopifyOrderNumber=1234567890&customerEmail=<EMAIL>
```

### 2. Test HTTPS and Security
1. Verify HTTPS works correctly
2. Test that no sensitive data is exposed
3. Verify middleware allows public access
4. Test CORS settings if needed

## Common Issues and Solutions

### Issue: "Order not found"
- Check if the order exists in the database
- Verify the order number format
- Check backend logs for errors

### Issue: Page shows loading forever
- Check if the backend API is running
- Verify the API endpoint URL is correct
- Check browser console for errors

### Issue: Server-side rendering not working
- Verify the page component is using `async` function
- Check that `fetchPublicOrderStatus` is being called server-side
- Verify Next.js is configured correctly

### Issue: Email links not working
- Check URL encoding for special characters
- Verify middleware allows public access
- Test the exact URL format from email
