'use server'

import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL;

export interface PublicOrderStatusResponse {
  orderNumber: string;
  orderDate: string;
  orderStatus: string;
  customerFirstName: string;
  customerLastName: string;
  itemCount: number;
  lineItems: Array<{
    id: string;
    itemNumber: string;
    productSku: {
      sku: string;
    };
    quantity: number;
    status: string;
  }>;
}

export async function fetchPublicOrderStatus(orderNumber: string): Promise<PublicOrderStatusResponse> {
  try {
    // Create a new axios instance without authentication for public endpoints
    const publicApiClient = axios.create({
      baseURL: API_URL,
      withCredentials: false,
    });

    const response = await publicApiClient.get(`/orders/public/status/${orderNumber}`);
    
    if (!response.data) {
      throw new Error('No data returned from server');
    }

    return response.data;
  } catch (error) {
    console.error('Error fetching public order status:', error);
    
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 404) {
        throw new Error('Order not found. Please check your order number and try again.');
      }
      if (error.response?.status >= 500) {
        throw new Error('Server error. Please try again later.');
      }
      throw new Error(error.response?.data?.message || 'Failed to fetch order status');
    }
    
    throw new Error('Failed to fetch order status');
  }
}

export async function requestNewImage(orderNumber: string, lineItemId: string, reason: string): Promise<void> {
  try {
    // This would be implemented when the backend endpoint is ready
    // For now, we'll just log the request
    console.log('Request new image:', { orderNumber, lineItemId, reason });
    
    // TODO: Implement actual API call when backend endpoint is available
    // const publicApiClient = axios.create({
    //   baseURL: API_URL,
    //   withCredentials: false,
    // });
    
    // await publicApiClient.post(`/orders/public/request-new-image`, {
    //   orderNumber,
    //   lineItemId,
    //   reason
    // });
    
    throw new Error('Request new image functionality is not yet implemented');
  } catch (error) {
    console.error('Error requesting new image:', error);
    throw error;
  }
}
