import React from 'react';
import { Metadata } from 'next';
import PublicOrderStatusPage from '@/views/PublicOrderStatus';
import { fetchPublicOrderStatus } from '@/actions/public-order-status';
import { PublicOrderStatus } from '@/types/public-order-status.types';

export const metadata: Metadata = {
  title: 'Order Status - Track Your Order',
  description: 'Check the status of your order and track its progress.',
};

interface OrderStatusWithNumberPageProps {
  params: { orderNumber: string };
}

const OrderStatusWithNumberPage: React.FC<OrderStatusWithNumberPageProps> = async ({ params }) => {
  let orderData: PublicOrderStatus | null = null;
  let error: string | null = null;

  // Fetch data server-side using the order number from URL
  try {
    orderData = await fetchPublicOrderStatus(params.orderNumber);
  } catch (err) {
    error = err instanceof Error ? err.message : 'Failed to fetch order status';
  }

  return (
    <PublicOrderStatusPage
      initialOrderData={orderData}
      initialError={error}
      shopifyOrderNumber={params.orderNumber}
    />
  );
};

export default OrderStatusWithNumberPage;
