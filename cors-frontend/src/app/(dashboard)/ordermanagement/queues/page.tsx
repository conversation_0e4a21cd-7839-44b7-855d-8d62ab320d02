import LoadingView from '@/components/LoadingView';
import { PageProps } from '../../../../../.next/types/app/(dashboard)/ordermanagement/products/page';
import QueuesWrapper from '@/views/order-management/queues';
import { Suspense } from 'react';
import { apiCall } from '@/actions/common.actions';
import { TabQueues } from '@/types/queues.types';

const QueuesManagementData = async () => {
  try {
    const response: TabQueues[] = await apiCall('get', '/workflow-queues/all');
    return <QueuesWrapper allQueues={response || []} />;
  } catch (error) {
    console.error('Failed to fetch data:', error);
    return <div>Failed to load data. Please try again later.</div>;
  }
};
const QueuesManagementPage = async (Props: PageProps) => {
  return (
    <Suspense fallback={<LoadingView />}>
      <QueuesManagementData />
    </Suspense>
  );
};

export default QueuesManagementPage;
