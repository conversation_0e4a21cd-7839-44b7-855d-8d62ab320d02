'use client'

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  <PERSON>alogContent,
  <PERSON>alogA<PERSON>,
  Button,
  TextField,
  Box,
  Typography,
  Card,
  CardMedia,
  Alert,
  Input
} from '@mui/material';
import { toast } from 'react-toastify';
import { PublicLineItem, CustomerContactForm } from '@/types/public-order-status.types';

interface CustomerContactFormProps {
  open: boolean;
  onClose: () => void;
  lineItem: PublicLineItem | null;
  onSubmit: (formData: CustomerContactForm) => Promise<void>;
}

const CustomerContactFormComponent: React.FC<CustomerContactFormProps> = ({
  open,
  onClose,
  lineItem,
  onSubmit
}) => {
  const [formData, setFormData] = useState<CustomerContactForm>({
    lineItemId: lineItem?.id || '',
    customerText: ''
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setFormData(prev => ({ ...prev, newImage: file }));
    }
  };

  const handleSubmit = async () => {
    if (!formData.customerText.trim()) {
      toast.error('Please provide your response');
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({
        ...formData,
        lineItemId: lineItem?.id || '',
        newImage: selectedFile || undefined
      });
      toast.success('Response submitted successfully');
      onClose();
    } catch (error) {
      toast.error('Failed to submit response');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      lineItemId: '',
      customerText: ''
    });
    setSelectedFile(null);
    onClose();
  };

  if (!lineItem) return null;

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Customer Contact Response - Item #{lineItem.itemNumber || lineItem.id.slice(-8)}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Product SKU: {lineItem.productSku?.sku}
          </Typography>
        </Box>

        {/* Show selected image and message */}
        {lineItem.selectedImage && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Reference Image
            </Typography>
            <Card sx={{ maxWidth: 300, mb: 2 }}>
              <CardMedia
                component="img"
                height="200"
                image={lineItem.selectedImage.url}
                alt="Reference image"
              />
            </Card>
            {lineItem.selectedImage.message && (
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Message from our team:</strong> {lineItem.selectedImage.message}
                </Typography>
              </Alert>
            )}
          </Box>
        )}

        {/* Upload new image section */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Upload New Image (Optional)
          </Typography>
          
          <Input
            type="file"
            inputProps={{ accept: 'image/*' }}
            onChange={handleFileChange}
            sx={{ mb: 2 }}
          />
          {selectedFile && (
            <Typography variant="body2" color="success.main">
              Selected: {selectedFile.name}
            </Typography>
          )}
        </Box>

        {/* Customer response field */}
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Your Response"
            placeholder="Please provide your response to our team's message..."
            value={formData.customerText}
            onChange={(e) => setFormData(prev => ({ 
              ...prev, 
              customerText: e.target.value 
            }))}
            required
          />
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Response'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CustomerContactFormComponent;
