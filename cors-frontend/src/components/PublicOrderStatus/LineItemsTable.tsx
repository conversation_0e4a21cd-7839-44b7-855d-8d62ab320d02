'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Box,
  Alert,
} from '@mui/material';
import { format } from 'date-fns';
import { toast } from 'react-toastify';
import PublicStatusChip from './PublicStatusChip';
import NewImageRequestForm from './NewImageRequestForm';
import CustomerContactForm from './CustomerContactForm';
import CustomerApprovalForm from './CustomerApprovalForm';
import {
  PublicLineItem,
  FormType,
  NewImageRequestForm as NewImageRequestFormType,
  CustomerContactForm as CustomerContactFormType,
  CustomerApprovalForm as CustomerApprovalFormType,
} from '@/types/public-order-status.types';
import { requestNewImage } from '@/actions/public-order-status';

interface LineItemsTableProps {
  lineItems: PublicLineItem[];
  orderNumber: string;
  formType?: FormType;
}

const LineItemsTable: React.FC<LineItemsTableProps> = ({ lineItems, orderNumber, formType }) => {
  const [requestDialogOpen, setRequestDialogOpen] = useState(false);
  const [selectedLineItem, setSelectedLineItem] = useState<PublicLineItem | null>(null);
  const [requestReason, setRequestReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [isApprovalMode, setIsApprovalMode] = useState(true);

  const handleRequestNewImage = (lineItem: PublicLineItem) => {
    setSelectedLineItem(lineItem);
    setRequestDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setRequestDialogOpen(false);
    setSelectedLineItem(null);
    setRequestReason('');
  };

  const renderActionButton = (item: PublicLineItem) => {
    if (!formType) {
      return (
        <Button
          size="small"
          variant="outlined"
          color="primary"
          onClick={() => handleRequestNewImage(item)}
          disabled={item.status === 'cancelled'}
        >
          Request New Image
        </Button>
      );
    }

    switch (formType) {
      case 'newImageRequest':
        return (
          <Button
            size="small"
            variant="contained"
            color="primary"
            onClick={() => handleRequestNewImage(item)}
          >
            Upload New Image
          </Button>
        );
      case 'customerContactNeeded':
        return (
          <Button
            size="small"
            variant="contained"
            color="info"
            onClick={() => handleRequestNewImage(item)}
          >
            Respond
          </Button>
        );
      case 'customerApproval':
        return (
          <Box display="flex" gap={1}>
            <Button
              size="small"
              variant="contained"
              color="success"
              onClick={() => handleApproval(item, true)}
            >
              Approve
            </Button>
            <Button
              size="small"
              variant="outlined"
              color="warning"
              onClick={() => handleApproval(item, false)}
            >
              Request Revision
            </Button>
          </Box>
        );
      default:
        return null;
    }
  };

  const handleApproval = (item: PublicLineItem, approved: boolean) => {
    setSelectedLineItem(item);
    setIsApprovalMode(approved);
    setApprovalDialogOpen(true);
  };

  const handleNewImageRequest = async (formData: NewImageRequestFormType) => {
    try {
      // TODO: Implement API call for new image request
      console.log('New Image Request:', formData);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('New image request submitted successfully');
    } catch (error) {
      throw new Error('Failed to submit new image request');
    }
  };

  const handleCustomerContact = async (formData: CustomerContactFormType) => {
    try {
      // TODO: Implement API call for customer contact response
      console.log('Customer Contact Response:', formData);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Response submitted successfully');
    } catch (error) {
      throw new Error('Failed to submit response');
    }
  };

  const handleCustomerApproval = async (formData: CustomerApprovalFormType) => {
    try {
      // TODO: Implement API call for customer approval
      console.log('Customer Approval:', formData);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      if (formData.approved) {
        toast.success('Item approved successfully! Status changed to "Ready for Vendor"');
      } else {
        toast.success(
          'Revision request submitted successfully! Status changed to "Revision Requested"',
        );
      }
    } catch (error) {
      throw new Error('Failed to process approval');
    }
  };

  const handleSubmitRequest = async () => {
    if (!selectedLineItem || !requestReason.trim()) {
      toast.error('Please provide a reason for the request');
      return;
    }

    setIsSubmitting(true);
    try {
      await requestNewImage(orderNumber, selectedLineItem.id, requestReason.trim());
      toast.success('Your request has been submitted successfully');
      handleCloseDialog();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to submit request');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!lineItems || lineItems.length === 0) {
    return (
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Items
          </Typography>
          <Alert severity="info">No items found for this order.</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Items ({lineItems.length})
          </Typography>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Item #</TableCell>
                  <TableCell>Product SKU</TableCell>
                  <TableCell align="center">Quantity</TableCell>
                  <TableCell>Current Status</TableCell>
                  <TableCell>Last Updated</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {lineItems.map(item => (
                  <TableRow key={item.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight={500}>
                        {item.itemNumber || item.id.slice(-8)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{item.productSku?.sku || 'N/A'}</Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{item.quantity}</Typography>
                    </TableCell>
                    <TableCell>
                      <PublicStatusChip
                        status={item.currentStatus || item.status}
                        variant="lineItem"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {item.lastUpdatedAt
                          ? format(new Date(item.lastUpdatedAt), 'MMM dd, yyyy')
                          : 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">{renderActionButton(item)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Form Components based on form type */}
      {formType === 'newImageRequest' && (
        <NewImageRequestForm
          open={requestDialogOpen}
          onClose={handleCloseDialog}
          lineItem={selectedLineItem}
          onSubmit={handleNewImageRequest}
        />
      )}

      {formType === 'customerContactNeeded' && (
        <CustomerContactForm
          open={requestDialogOpen}
          onClose={handleCloseDialog}
          lineItem={selectedLineItem}
          onSubmit={handleCustomerContact}
        />
      )}

      {formType === 'customerApproval' && (
        <CustomerApprovalForm
          open={approvalDialogOpen}
          onClose={() => setApprovalDialogOpen(false)}
          lineItem={selectedLineItem}
          isApproval={isApprovalMode}
          onSubmit={handleCustomerApproval}
        />
      )}

      {/* Default dialog for regular request new image */}
      {!formType && (
        <Dialog open={requestDialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Request New Image</DialogTitle>
          <DialogContent>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Item: {selectedLineItem?.itemNumber || selectedLineItem?.id.slice(-8)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                SKU: {selectedLineItem?.productSku?.sku}
              </Typography>
            </Box>

            <TextField
              autoFocus
              margin="dense"
              label="Reason for Request"
              fullWidth
              multiline
              rows={4}
              variant="outlined"
              value={requestReason}
              onChange={e => setRequestReason(e.target.value)}
              placeholder="Please describe why you need a new image for this item..."
              required
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmitRequest}
              variant="contained"
              disabled={isSubmitting || !requestReason.trim()}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Request'}
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </>
  );
};

export default LineItemsTable;
