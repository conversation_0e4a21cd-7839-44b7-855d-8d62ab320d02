'use client';
import Button from '@/components/Button';
import { Box } from '@mui/material';
import Grid from '@mui/material/Grid2';
import SingleImageViewCard from '@/components/card-statistics/SingleImageViewCard';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import { SingleQueueItem } from '@/types/queues.types';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import useApiCall from '@/hooks/useApiCall';
import LoadingView from '@/components/LoadingView';
import ProductAttributesViewer from '../../components/ProductAttributesViewer';
import { toast } from 'react-toastify';

type CropReviewFormType = {
  image: string[];
};
const CropNeededQueue = ({
  queueItem,
  queueId,
  actionType,
}: {
  queueItem: SingleQueueItem | undefined;
  queueId: string;
  actionType: string;
}) => {
  const router = useRouter();
  const [upcomingQueue, setUpcomingQueue] = useState<SingleQueueItem | undefined>(undefined);
  const { isLoading: loading, makeRequest: requestUpdateQueueItem } = useApiCall<SingleQueueItem>(
    `/workflow-queues/upload-completed-art-file`,
    'post',
    false,
  );

  useEffect(() => {
    if (upcomingQueue) {
      if (upcomingQueue?.attachments == null) {
        router.push('/ordermanagement/queues');
      }
    }
  }, [upcomingQueue, router]);

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CropReviewFormType>({
    resolver: yupResolver(
      yup.object().shape({
        image: yup
          .array()
          .of(yup.string().required())
          .min(1, 'At least one image is required')
          .max(1, 'Only one image is allowed')
          .required('Image is required'),
      }),
    ),
    defaultValues: {
      image: [],
    },
  });
  const onSubmit = async (data: CropReviewFormType) => {
    const response = await requestUpdateQueueItem({
      body: {
        attachmentId: queueItem?.attachments?.id,
        completedArtFileUrl: data?.image[0],
        queueId: queueId,
      },
    });
    if (response) {
      setUpcomingQueue(response);
      toast.success('Queue item updated successfully');
    }
  };

  if (loading) return <LoadingView />;

  if (upcomingQueue?.attachments) {
    return <CropNeededQueue queueItem={upcomingQueue} queueId={queueId} actionType={actionType} />;
  }

  return (
    <>
      <ProductAttributesViewer actionType={actionType} queueItem={queueItem} />
      <Grid container spacing={4} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 6 }}>
          <SingleImageViewCard
            imageUrl={queueItem?.attachments?.attachment_url || ''}
            title="Customer Image"
            downloadUrl={queueItem?.attachments?.attachment_url || ''}
          />
        </Grid>
        {queueItem?.attachments?.cutout_pro_url && (
          <Grid size={{ xs: 12, md: 6 }}>
            <SingleImageViewCard
              imageUrl={queueItem?.attachments?.cutout_pro_url || ''}
              title="Denied Image"
              downloadUrl={queueItem?.attachments?.cutout_pro_url || ''}
              isDanger={true}
            />
          </Grid>
        )}
      </Grid>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name={`image`}
          control={control}
          rules={{ required: 'Image is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <ImageUploadField
              control={control}
              title="Completed Art File"
              buttonText="Upload Art File"
              errors={errors}
              setValue={setValue}
              name={`image`}
              minImages={1}
              maxImages={1}
              maxFileSizeMB={5}
              formValue={value}
              errorField={error?.message}
              uploaderType="file"
            />
          )}
        />
        {/* Actions Section */}
        <Box maxWidth="600px" mx="auto">
          <Grid container spacing={2} justifyContent="center">
            <Grid size={{ xs: 12, sm: 4 }}>
              <Button
                variant="outlined"
                fullWidth
                size="small"
                title="Submit"
                type="submit"
                disabled={isSubmitting}
              />
            </Grid>
          </Grid>
        </Box>
      </form>
    </>
  );
};

export default CropNeededQueue;
