'use client';
import Button from '@/components/Button';
import { Box } from '@mui/material';
import Grid from '@mui/material/Grid2';
import SingleImageViewCard from '@/components/card-statistics/SingleImageViewCard';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
type FormType = {
  image: string[];
};
const TemplatePlacementQueue = () => {
  const {
    handleSubmit,
    control,
    setValue,

    formState: { errors, isSubmitting },
  } = useForm<FormType>({
    resolver: yupResolver(
      yup.object().shape({
        image: yup
          .array()
          .of(yup.string().required())
          .min(1, 'At least one art file is required')
          .max(1, 'Only one art file is allowed')
          .required('Art file is required'),
      }),
    ),
    defaultValues: {
      image: [],
    },
  });
  const onSubmit = async (data: FormType) => {};
  return (
    <>
      {/* Images Section */}
      <Grid container spacing={4} sx={{ mb: 4 }}>
        {Array.from({ length: 3 }).map((_, index) => (
          <Grid key={index} size={{ xs: 12, md: Array.from({ length: 3 }).length >= 3 ? 4 : 6 }}>
            <SingleImageViewCard
              imageUrl="/images/avatars/1.png"
              title={`Pet ${index + 1}`}
              downloadUrl="/images/avatars/1.png"
            />
          </Grid>
        ))}
      </Grid>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name={`image`}
          control={control}
          rules={{ required: 'Image is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <ImageUploadField
              control={control}
              errors={errors}
              setValue={setValue}
              name={`image`}
              minImages={1}
              maxImages={1}
              maxFileSizeMB={5}
              formValue={value}
              errorField={error?.message}
              title="Upload Art File"
              buttonText="Upload"
            />
          )}
        />
        {/* Actions Section */}
        <Box maxWidth="600px" mx="auto">
          <Grid container spacing={2} justifyContent="center">
            <Grid size={{ xs: 12, sm: 4 }}>
              <Button
                variant="outlined"
                fullWidth
                size="small"
                title="Submit"
                type="submit"
                disabled={isSubmitting}
              />
            </Grid>
          </Grid>
        </Box>
      </form>
    </>
  );
};

export default TemplatePlacementQueue;
