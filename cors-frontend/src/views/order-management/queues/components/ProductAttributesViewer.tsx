import { Box, Chip, Divider, Typography } from '@mui/material';
import React from 'react';
import Grid from '@mui/material/Grid2';
import { CropTypeEnum } from '@/constants/queue.constants';

const ProductAttributesViewer = ({
  actionType,
  queueItem,
}: {
  actionType: string;
  queueItem: any;
}) => {
  const attachment = queueItem?.attachments;
  let attributes: Array<{ name: string; value: string }> = [];
  switch (actionType) {
    case 'Crop Review':
    case 'Crop Needed':
      attributes = [
        {
          name: 'Priority',
          value: (
            <Chip
              label={attachment?.priority || 'Standard'}
              variant="outlined"
              color={attachment?.priority ? 'warning' : 'success'}
            />
          ),
        },
        {
          name: 'Status',
          value: attachment?.attachment_status || '-',
        },
        {
          name: 'Order Date',
          value: attachment?.order_date
            ? new Date(attachment.order_date).toLocaleDateString()
            : '-',
        },
        {
          name: 'Order Number',
          value: attachment?.order_number || '-',
        },
        {
          name: 'Crop Type',
          value: CropTypeEnum[attachment?.cropType as keyof typeof CropTypeEnum] || '-',
        },
      ];
      break;
    default:
      attributes = [];
  }

  return (
    <>
      <Divider sx={{ my: 4 }} />

      <Box sx={{ flexGrow: 1 }}>
        <Grid container spacing={4} alignItems="center">
          {attributes.map((item, index) => (
            <Grid size={4} key={index}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                }}
              >
                <Box sx={{ fontWeight: 'bold' }}>{item.name}:</Box>
                <Box>{item.value || '-'}</Box>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
      <Divider sx={{ my: 6 }} />
    </>
  );
};

export default ProductAttributesViewer;
