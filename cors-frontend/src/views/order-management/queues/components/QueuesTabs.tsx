'use client';
import { Box, Tab, Tabs, Typography } from '@mui/material';
import React, { useMemo } from 'react';
import QueuesTableWrapper from './QueuesTableWrapper';
import { TabQueues } from '@/types/queues.types';
import { useAbility } from '@/libs/casl/AbilityContext';
import { getViewPermissionForQueue } from '@/utils/queuePermissionUtils';

const QueuesTabs = ({ allQueues }: { allQueues: TabQueues[] }) => {
  const ability = useAbility();

  interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
  }

  // Check if user has permission for a specific queue
  const hasQueuePermission = (queueName: string) => {
    const permission = getViewPermissionForQueue(queueName);
    if (!permission) return false;
    return ability?.can(permission.action, permission.target);
  };

  const tabs = useMemo(
    () =>
      allQueues?.map(queue => ({
        label: `${queue.name}`,
        id: queue.id,
        content: <QueuesTableWrapper queue={queue} />,
        items_count: queue.item_count,
        disabled: !hasQueuePermission(queue.name),
      })),
    [allQueues, ability],
  );

  const CustomTabPanel = React.memo(function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;
    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
      </div>
    );
  });

  function a11yProps(index: number) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }

  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={value} onChange={handleChange} aria-label="queues tabs">
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              disabled={tab.disabled}
              label={`${tab.label} (${tab.items_count || 0})`}
              {...a11yProps(index)}
            />
          ))}
        </Tabs>
      </Box>
      {tabs.map((tab, index) => (
        <CustomTabPanel key={index} value={value} index={index}>
          {tab.disabled ? (
            <Box sx={{ p: 3, textAlign: 'center', color: 'text.secondary' }}>
              <Typography variant="h6" gutterBottom>
                Access Restricted
              </Typography>
              <Typography variant="body2">
                You don't have permission to view this queue. Contact your administrator for access.
              </Typography>
            </Box>
          ) : (
            tab.content
          )}
        </CustomTabPanel>
      ))}
    </>
  );
};

export default React.memo(QueuesTabs);
